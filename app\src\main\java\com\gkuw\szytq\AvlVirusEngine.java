package com.gkuw.szytq;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.antiy.avlsdk_cloud.AVLEngineCloud;
import com.antiy.avlsdk_pc.AVLEnginePC;
import com.avl.engine.AVLAppInfo;
import com.avl.engine.AVLEngine;
import com.gkuw.szytq.util.FileUtil;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AvlVirusEngine {
    private static final String TAG = AvlVirusEngine.class.getSimpleName();
    private static boolean sFlag = false;

    private static AvlVirusEngine mInstance;
    private static String mAVLPath;

    private static boolean mEnableSleep = false;
    private static boolean mEnableCpulimit = false;

    private static int mSleepMacroSec = 10; // macro means us
    private static int mInterval = 50; // milisec for monitor process interval
    private static int mPercent = 9; // percent for cpu usage, total

    private AvlVirusEngine() {
    }

    public static int getPid(Process p) {
        int pid = -1;

        try {
            Field f = p.getClass().getDeclaredField("pid");
            f.setAccessible(true);
            pid = f.getInt(p);
            f.setAccessible(false);
        } catch (Throwable ignored) {
            try {
                Matcher m = Pattern.compile("pid=(\\d+)").matcher(p.toString());
                pid = m.find() ? Integer.parseInt(m.group(1)) : -1;
            } catch (Throwable ignored2) {
                pid = -1;
            }
        }
        return pid;
    }

    public static AvlVirusEngine getInstance() {
        if (mInstance == null) {
            synchronized (AvlVirusEngine.class) {
                if (mInstance == null) {
                    mInstance = new AvlVirusEngine();
                }
            }
        }

        return mInstance;
    }

    public String getmAVLPath() {
        return mAVLPath;
    }

    /**
     * 单个文件扫描
     *
     * @param filePath
     * @return 返回结果 {@AVLScanResult}
     */
    public AvlVirusScanResult scanFile(String filePath) {
        File targetFile = new File(filePath);
        if (!targetFile.exists()) {
            return null;
        }

        AvlVirusScanResult scanResult = new AvlVirusScanResult();
        if (FileUtil.isForMobile(targetFile)) {
            Log.d(TAG, "called avlengine " + targetFile);
            //调用apk引擎扫描
            AVLAppInfo appInfo = AVLEngine.scan(filePath);
            if (appInfo != null) {
                Log.d(TAG, "avl virus call ok, result " + appInfo.getVirusName());
                scanResult.setMd5(appInfo.getApkMd5());
                scanResult.setPath(appInfo.getPath());
                scanResult.setDescription(appInfo.getVirusDescription());
                scanResult.setVirusName(appInfo.getVirusName());
                scanResult.setName(appInfo.getAppName());
                scanResult.setPkgName(appInfo.getPackageName());
            } else {
                Log.e(TAG, "avl virus call failed.");
            }
        } else { //否则就走媒体文件
            String malwareName = AVLEnginePC.scan(filePath);
            scanResult.setVirusName(malwareName);
            scanResult.setPath(filePath);
            scanResult.setMd5("");
        }
        return scanResult;
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public String scanFileCloud(Context contex, String filePath) {
        String apkInfoStr = AVLEngineCloud.getInstance().getApkInfo(filePath);
        return AVLEngineCloud.getInstance().scan(contex, filePath, apkInfoStr);
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public void scanDirCloud(Context context, String dir, AvlVirusScanListener scanListener) {
        scanListener.scanStart();
        //遍历路径，获取文件信息
        List<String> targetFiles = FileUtil.listAllFiles(new File(dir));
        //回调个数
        scanListener.scanCount(targetFiles.size());

        for (String filePath : targetFiles) {
            String apkInfo = AVLEngineCloud.getInstance().getApkInfo(filePath);
            AvlVirusScanResult scanResult = new AvlVirusScanResult();

            String result = AVLEngineCloud.getInstance().scan(context, filePath, apkInfo);

            // scanResult.setMd5(FileMd5Utils.getFileMd5(context, filePath));
            scanResult.setPath(filePath);
            scanResult.setVirusName(result);

            scanListener.scanSingleEnd(scanResult);
        }

        //回调扫描完成
        scanListener.scanFinished();
    }

    /**
     * 目录扫描
     *
     * @param dir
     * @return 调用是否成功
     */
    @SuppressLint("DefaultLocale")
    @RequiresApi(api = Build.VERSION_CODES.O)
    public void scanAll(String dir, AvlVirusScanListener scanListener) {
        Process p = null;
        Log.d(TAG, String.format("Cpulimit is enabled ? %b", mEnableCpulimit));
        if (mEnableCpulimit) {
            try {
                p = Runtime.getRuntime().exec(
                        String.format("%s -p %d -P %d -i %d",
                                CpuUsageMonitor.getInstance().mCpuUsagePath,
                                android.os.Process.myPid(),
                                mPercent,
                                mInterval));
                Log.d("com.antiy.cmd", String.format("running process %s[%s]",
                        CpuUsageMonitor.getInstance().mCpuUsagePath, p.toString()));
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (p == null) {
                return;
            }
        }

        //回调开始
        scanListener.scanStart();

        List<String> targetFiles = FileUtil.listAllFiles(new File(dir));

        //回调个数
        scanListener.scanCount(targetFiles.size());

        for (String f : targetFiles) {
            scanListener.scanSingleIng("", "", f);
            AvlVirusScanResult scanResult = scanFile(f);
            scanListener.scanSingleEnd(scanResult);

            if (mEnableSleep) {
                try {
                    // 参数 nanos 为int型，最大2147483647，大于 MAXINT/1000 * 1000，此处无问题
                    int ms = mSleepMacroSec / 1000;
                    int ns = (mSleepMacroSec - (mSleepMacroSec / 1000) * 1000) * 1000;
                    Log.d(TAG, String.format("sleep %dms_%dns before", ms, ns));
                    Thread.sleep(ms, ns);
                    Log.d(TAG, "sleep after");
                } catch (InterruptedException e) {
                    Log.e(TAG, "sleep error!");
                    // e.printStackTrace();
                }
            }
        }

        if (mEnableCpulimit) {
            if (p.isAlive()) {
                Log.d(TAG, String.format("Sending signal to %d of signal SIG_USR1", getPid(p)));
                // android.os.Process.sendSignal(getPid(p), android.os.Process.SIGNAL_USR1);

                try {
                    Runtime.getRuntime().exec(new String[]{"kill", "-SIGUSER", String.format("%d"
                            , getPid(p))});
                } catch (IOException e) {
                    e.printStackTrace();
                }

                // p.destroyForcibly();
                try {
                    p.waitFor(100, TimeUnit.MILLISECONDS);
                    if(p.isAlive()){
                        p.destroyForcibly();
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        //回调扫描完成
        scanListener.scanFinished();
    }

    public void updateConfig(SharedPreferences preferences) {
        mSleepMacroSec = preferences.getInt(MainActivity.PREF_USEC, 10);
        mInterval = preferences.getInt(MainActivity.PREF_INTERVAL, 50);
        mPercent = preferences.getInt(MainActivity.PREF_PERCENT, 9);

        mEnableSleep = preferences.getBoolean(MainActivity.PREF_SLEEP, false);
        mEnableCpulimit = preferences.getBoolean(MainActivity.PREF_CPULIMIT, false);
    }
}
