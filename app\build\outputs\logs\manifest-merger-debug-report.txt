-- Merging decision tree log ---
permission#com.gkuw.szytq.permission.ANTI_VIRUS
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:11:5-13:47
	android:protectionLevel
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:13:9-44
	android:name
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:12:9-62
provider#com.avl.engine.security.AVLProvider
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:41:9-44:75
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:41:9-44:75
	android:authorities
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:43:13-63
	android:permission
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:44:13-72
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:42:13-63
manifest
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
MERGED from [:avlsdk_pc] D:\Projects\Android\riskdemo\avlsdk_pc\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:avlsdk_cloud] D:\Projects\Android\riskdemo\avlsdk_cloud\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.core:core-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c436c78f924c5e98e4c83a540771cec\transformed\jetified-core-ktx-1.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\52bd49addc31bd1ac74cbc2841b1f6bd\transformed\material-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\769e108a2b0df7e3d550231fd23a7219\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\df38b7d82b8df78fa096e665b4e14f67\transformed\constraintlayout-1.1.3\AndroidManifest.xml:2:1-11:12
MERGED from [io.github.mayampi01:com.tbruyelle.rxpermissions2:0.9.5] C:\Users\<USER>\.gradle\caches\transforms-3\db9e9e10d869d250c9c5fc2ad139790d\transformed\jetified-com.tbruyelle.rxpermissions2-0.9.5\AndroidManifest.xml:2:1-13:12
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dc55f45e27aabd73657f4dcc4a426475\transformed\jetified-rxandroid-2.0.1\AndroidManifest.xml:15:1-21:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b44e820948500b6e9673505de903257\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7143aeb013003040f7152d146db83152\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b600fd2b3adb76866ab7925b8093de7\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f76c752cde06b1e60da72047773f00a4\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\28d71797ec7bd35c92027aa571c243a7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\713dd1c9854e786b1c7e43b166306572\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3ce4c3c73cddf2357c797e7986c856d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\11ac1f9c0a6d7c56d243e00149bfc94e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf57ad78307b5305333fa6dc27ff37f5\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5134f782ceadc1674f3c9e540c18755\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2f7058eea301ad27ad31f54518804e4\transformed\jetified-activity-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aebd0a7b7987f076ebe9443550b3d5cc\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd73fb8b550f7c0198a39d8ebbd51906\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38724ab069d0edc6dbab885f6450a45\transformed\core-1.1.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\076b8bd5c6991c3b1b16adee5984a83b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddb57186edeee347e4cb08e386a536e0\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a88fb67164c02f7477dbb3f78050d386\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f279a96a6334b38cf6146393181acec\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6642cca378db1dea715454c908531b39\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\500974d7f0bd6f0d983d3c5e0e9be612\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ddfb7e9d835771d68a2ccbaf3a72ba0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f94d91996838f5d842bcb883de8c39a\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a427c6ee3ceabc74d7db3aaeb63ba78\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\21f37e18fb3996893af2bea58a0b0129\transformed\core-runtime-2.0.0\AndroidManifest.xml:17:1-22:12
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
	package
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:3:5-29
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:1-46:12
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:9:5-10:38
	android:minSdkVersion
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:9:22-79
application
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:15:5-45:19
MERGED from [com.google.android.material:material:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\52bd49addc31bd1ac74cbc2841b1f6bd\transformed\material-1.1.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\52bd49addc31bd1ac74cbc2841b1f6bd\transformed\material-1.1.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\df38b7d82b8df78fa096e665b4e14f67\transformed\constraintlayout-1.1.3\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\df38b7d82b8df78fa096e665b4e14f67\transformed\constraintlayout-1.1.3\AndroidManifest.xml:9:5-20
MERGED from [io.github.mayampi01:com.tbruyelle.rxpermissions2:0.9.5] C:\Users\<USER>\.gradle\caches\transforms-3\db9e9e10d869d250c9c5fc2ad139790d\transformed\jetified-com.tbruyelle.rxpermissions2-0.9.5\AndroidManifest.xml:11:5-20
MERGED from [io.github.mayampi01:com.tbruyelle.rxpermissions2:0.9.5] C:\Users\<USER>\.gradle\caches\transforms-3\db9e9e10d869d250c9c5fc2ad139790d\transformed\jetified-com.tbruyelle.rxpermissions2-0.9.5\AndroidManifest.xml:11:5-20
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38724ab069d0edc6dbab885f6450a45\transformed\core-1.1.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38724ab069d0edc6dbab885f6450a45\transformed\core-1.1.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a88fb67164c02f7477dbb3f78050d386\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a88fb67164c02f7477dbb3f78050d386\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38724ab069d0edc6dbab885f6450a45\transformed\core-1.1.0\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:21:9-35
	android:label
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:19:9-41
	android:roundIcon
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:20:9-54
	android:icon
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:18:9-43
	android:allowBackup
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:17:9-35
	android:theme
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:22:9-40
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:16:9-38
activity#com.gkuw.szytq.MainActivity
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:24:9-32:20
	android:exported
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:26:13-36
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:25:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:27:13-31:29
action#android.intent.action.MAIN
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:28:17-69
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:28:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:30:27-74
meta-data#AVL_SDK_APPKEY
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:34:9-36:64
	android:value
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:36:13-61
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:35:13-42
meta-data#com.antiy.ccs.app.App
ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:37:9-39:43
	android:value
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:39:13-40
	android:name
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:38:13-49
uses-sdk
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
MERGED from [:avlsdk_pc] D:\Projects\Android\riskdemo\avlsdk_pc\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:avlsdk_pc] D:\Projects\Android\riskdemo\avlsdk_pc\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:avlsdk_cloud] D:\Projects\Android\riskdemo\avlsdk_cloud\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:avlsdk_cloud] D:\Projects\Android\riskdemo\avlsdk_cloud\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c436c78f924c5e98e4c83a540771cec\transformed\jetified-core-ktx-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c436c78f924c5e98e4c83a540771cec\transformed\jetified-core-ktx-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.material:material:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\52bd49addc31bd1ac74cbc2841b1f6bd\transformed\material-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\52bd49addc31bd1ac74cbc2841b1f6bd\transformed\material-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\769e108a2b0df7e3d550231fd23a7219\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\769e108a2b0df7e3d550231fd23a7219\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\df38b7d82b8df78fa096e665b4e14f67\transformed\constraintlayout-1.1.3\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\df38b7d82b8df78fa096e665b4e14f67\transformed\constraintlayout-1.1.3\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.mayampi01:com.tbruyelle.rxpermissions2:0.9.5] C:\Users\<USER>\.gradle\caches\transforms-3\db9e9e10d869d250c9c5fc2ad139790d\transformed\jetified-com.tbruyelle.rxpermissions2-0.9.5\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.mayampi01:com.tbruyelle.rxpermissions2:0.9.5] C:\Users\<USER>\.gradle\caches\transforms-3\db9e9e10d869d250c9c5fc2ad139790d\transformed\jetified-com.tbruyelle.rxpermissions2-0.9.5\AndroidManifest.xml:7:5-9:41
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dc55f45e27aabd73657f4dcc4a426475\transformed\jetified-rxandroid-2.0.1\AndroidManifest.xml:19:5-43
MERGED from [io.reactivex.rxjava2:rxandroid:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dc55f45e27aabd73657f4dcc4a426475\transformed\jetified-rxandroid-2.0.1\AndroidManifest.xml:19:5-43
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b44e820948500b6e9673505de903257\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b44e820948500b6e9673505de903257\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7143aeb013003040f7152d146db83152\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7143aeb013003040f7152d146db83152\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b600fd2b3adb76866ab7925b8093de7\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b600fd2b3adb76866ab7925b8093de7\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f76c752cde06b1e60da72047773f00a4\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f76c752cde06b1e60da72047773f00a4\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\28d71797ec7bd35c92027aa571c243a7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\28d71797ec7bd35c92027aa571c243a7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\713dd1c9854e786b1c7e43b166306572\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\713dd1c9854e786b1c7e43b166306572\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3ce4c3c73cddf2357c797e7986c856d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a3ce4c3c73cddf2357c797e7986c856d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\11ac1f9c0a6d7c56d243e00149bfc94e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\11ac1f9c0a6d7c56d243e00149bfc94e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf57ad78307b5305333fa6dc27ff37f5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf57ad78307b5305333fa6dc27ff37f5\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5134f782ceadc1674f3c9e540c18755\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5134f782ceadc1674f3c9e540c18755\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2f7058eea301ad27ad31f54518804e4\transformed\jetified-activity-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e2f7058eea301ad27ad31f54518804e4\transformed\jetified-activity-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aebd0a7b7987f076ebe9443550b3d5cc\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\aebd0a7b7987f076ebe9443550b3d5cc\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd73fb8b550f7c0198a39d8ebbd51906\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd73fb8b550f7c0198a39d8ebbd51906\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38724ab069d0edc6dbab885f6450a45\transformed\core-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38724ab069d0edc6dbab885f6450a45\transformed\core-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\076b8bd5c6991c3b1b16adee5984a83b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\076b8bd5c6991c3b1b16adee5984a83b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddb57186edeee347e4cb08e386a536e0\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddb57186edeee347e4cb08e386a536e0\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a88fb67164c02f7477dbb3f78050d386\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a88fb67164c02f7477dbb3f78050d386\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f279a96a6334b38cf6146393181acec\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f279a96a6334b38cf6146393181acec\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6642cca378db1dea715454c908531b39\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6642cca378db1dea715454c908531b39\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\500974d7f0bd6f0d983d3c5e0e9be612\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\500974d7f0bd6f0d983d3c5e0e9be612\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ddfb7e9d835771d68a2ccbaf3a72ba0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ddfb7e9d835771d68a2ccbaf3a72ba0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f94d91996838f5d842bcb883de8c39a\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f94d91996838f5d842bcb883de8c39a\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a427c6ee3ceabc74d7db3aaeb63ba78\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a427c6ee3ceabc74d7db3aaeb63ba78\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\21f37e18fb3996893af2bea58a0b0129\transformed\core-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\21f37e18fb3996893af2bea58a0b0129\transformed\core-runtime-2.0.0\AndroidManifest.xml:20:5-44
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
		INJECTED from D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
