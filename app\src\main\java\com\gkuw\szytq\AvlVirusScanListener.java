package com.gkuw.szytq;

public interface AvlVirusScanListener {
    void scanStart();

    void scanCount(int i);

    /**
     * @param name 文件名/应用名
     * @param name2 文件别名/包名
     * @param samplePath 文件路径
     */
    void scanSingleIng(String name, String name2, String samplePath);

    /**
     * 单个扫描完成回调
     * @param scanResult 扫描结果
     */
    void scanSingleEnd(AvlVirusScanResult scanResult);

    void scanStop();

    void scanFinished();

    void onCrash();
}
