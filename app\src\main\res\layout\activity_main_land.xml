<?xml version="1.0" encoding="utf-8"?>
<GridLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:columnCount="4"
    android:contentDescription="扫描间隔"
    android:orientation="vertical"
    android:rowCount="12">

    <Button
        android:id="@+id/btn_scanDir"
        android:layout_row="0"
        android:layout_columnSpan="3"
        android:layout_column="0"
        android:text="开始扫描"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/editTextTextPersonName"
        android:layout_row="1"
        android:layout_column="0"
        android:layout_columnSpan="3"
        android:ems="10"
        android:gravity="center_horizontal"
        android:text="切换类型"
        android:textSize="20sp" />

    <Button
        android:id="@+id/btn_setJar"
        android:layout_row="2"
        android:layout_column="0"
        android:text="jar"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_setDex"
        android:layout_row="2"
        android:layout_column="1"
        android:text="dex"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_setApk"
        android:layout_row="2"
        android:layout_column="2"
        android:text="apk"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_setMp3"
        android:layout_row="3"
        android:layout_column="0"
        android:text="mp3"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_setMp4"
        android:layout_row="3"
        android:layout_column="1"
        android:text="mp4"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_setJpg"
        android:layout_row="3"
        android:layout_column="2"
        android:text="jpg"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_setPng"
        android:layout_row="3"
        android:layout_column="0"
        android:text="png"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_setGif"
        android:layout_row="3"
        android:layout_column="1"
        android:text="gif"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_setElf"
        android:layout_row="3"
        android:layout_column="2"
        android:text="elf"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/textView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="扫描路径"
        android:textSize="18sp" />

    <EditText
        android:id="@+id/edt_scanDir"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="3"
        android:text="/sdcard/samples/jar" />

    <Button
        android:id="@+id/mBtnResetMonitor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="重置"
        android:textSize="16sp" />
    <Switch
        android:id="@+id/toggleSleep"
        android:layout_row="4"
        android:layout_column="0"
        android:layout_columnSpan="3"
        android:layout_marginTop="6sp"
        android:text="扫描间隔" />

    <EditText
        android:id="@+id/inputPercent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ems="10"
        android:hint="percent, default 9"
        android:inputType="number"
        android:textSize="14sp" />

    <Button
        android:id="@+id/mBtnConfirmPercent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="确认" />

    <EditText
        android:id="@+id/inputInterval"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ems="10"
        android:hint="interval, default 50"
        android:inputType="number"
        android:textSize="14sp" />

    <Button
        android:id="@+id/mBtnConfirmInterval"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="确认" />

    <EditText
        android:id="@+id/inputSleepMacroSec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ems="10"
        android:inputType="number" />

    <TextView
        android:id="@+id/labelSleepMacroSec"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="us" />

    <Button
        android:id="@+id/btnSleepMicroSecConfirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="确认"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btnSleepMacroSecReset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="重置"
        android:textSize="16sp" />


    <Switch
        android:id="@+id/toggleCpuLimit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8sp"
        android:text="CPULimit" />

    <TextView
        android:id="@+id/tv_prompt"
        android:layout_row="1"
        android:layout_rowSpan="9"
        android:layout_column="2"
        android:layout_columnSpan="1" />

</GridLayout>
