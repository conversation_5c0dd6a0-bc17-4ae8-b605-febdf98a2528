@startuml 激活流程

' 让响应消息的文本在箭头下方
skinparam ResponseMessageBelowArrow true

' 设置导出图形的字体和字号
skinparam DefaultFontName "Microsoft YaHei"
skinparam DefaultFontSize 12

participant 引擎so as so
participant SDK as sdk
participant 服务端业务 as server
participant 方向T as T
participant 方向L as L

group 申请License

        T -> L :提交数据
        note over T,L
                start, dead, offline
                uuid(client id)
        end note

        T <-- L: 授权License & 配套SDK

end

group 激活服务端
        T -> server : 收集硬件数据
        note over T,server
                hardware_id
        end note
        server --> T : 硬件数据
        T -> L : 提交硬件信息
        T <-- L : 返回授权license
        T -> server : 应用license
end

group 使用SDK

        group 初次激活
                sdk -> server : 发送激活请求
                note over sdk,server
                        uuid/vid
                        uuid: sdk 标识符
                        vid: vehicle id
                end note
                sdk <-- server: 响应授权数据
                note over sdk,server
                        start, dead, offline
                end note

                sdk -> sdk : 保存授权数据

                group 应用授权数据到引擎so
                sdk -> sdk : 判断标识符
                so -> so : 判断start/dead
                so -> so : 判断离线超时
                end
        end

        alt 未过期，未超时
                note over so, server
                        存在有效的本地授权数据
                end note
                sdk -> sdk : 加载保存的授权数据

                sdk -> so : 应用授权数据到引擎so

                note over so,server
                        发起激活请求
                        流程同初次激活
                end note

        else 未过期，已超时
                note over so,server
                        本地授权数据失效
                end note

                note over so,server
                        发起激活请求
                        流程同初次激活
                end note

        else 已过期，未超时
                note over so,server
                        本地授权数据失效
                end note

                note over so,server
                        发起激活请求
                        流程同初次激活
                end note
        
        else 已过期，已超时
                note over so,server
                        本地授权数据失效
                end note

                note over so,server
                        发起激活请求
                        流程同初次激活
                end note
        end

end

@enduml
