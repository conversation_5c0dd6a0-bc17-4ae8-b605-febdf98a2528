1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gkuw.szytq"
4    android:versionCode="21"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="31" />
9-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:7:5-80
13-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:8:5-81
14-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission
15-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
16-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:9:22-79
17        android:minSdkVersion="30" />
17-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:10:9-35
18
19    <permission
20        android:name="com.gkuw.szytq.permission.ANTI_VIRUS"
20-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:12:9-62
21        android:protectionLevel="signature" />
21-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:13:9-44
22
23    <application
23-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:15:5-45:19
24        android:name="com.gkuw.szytq.MyApplication"
24-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:16:9-38
25        android:allowBackup="true"
25-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:17:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38724ab069d0edc6dbab885f6450a45\transformed\core-1.1.0\AndroidManifest.xml:24:18-86
27        android:debuggable="true"
28        android:icon="@mipmap/ic_launcher"
28-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:18:9-43
29        android:label="@string/app_name"
29-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:19:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:20:9-54
31        android:supportsRtl="true"
31-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:21:9-35
32        android:testOnly="true"
33        android:theme="@style/AppTheme" >
33-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:22:9-40
34        <activity
34-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:24:9-32:20
35            android:name="com.gkuw.szytq.MainActivity"
35-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:25:13-41
36            android:exported="true" >
36-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:26:13-36
37            <intent-filter>
37-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:27:13-31:29
38                <action android:name="android.intent.action.MAIN" />
38-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:28:17-69
38-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:28:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:30:17-77
40-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:30:27-74
41            </intent-filter>
42        </activity>
43
44        <meta-data
44-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:34:9-36:64
45            android:name="AVL_SDK_APPKEY"
45-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:35:13-42
46            android:value="C33742EFF27CCD51BA3EC1FB0E6FE22E" />
46-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:36:13-61
47        <meta-data
47-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:37:9-39:43
48            android:name="com.antiy.ccs.app.App"
48-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:38:13-49
49            android:value="Application" />
49-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:39:13-40
50
51        <provider
52            android:name="com.avl.engine.security.AVLProvider"
52-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:42:13-63
53            android:authorities="com.gkuw.szytq.AVLprovider"
53-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:43:13-63
54            android:permission="com.gkuw.szytq.permission.ANTI_VIRUS" />
54-->D:\Projects\Android\riskdemo\app\src\main\AndroidManifest.xml:44:13-72
55    </application>
56
57</manifest>
