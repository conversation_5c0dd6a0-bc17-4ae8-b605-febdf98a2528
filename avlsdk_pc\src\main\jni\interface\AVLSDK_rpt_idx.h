#ifndef	__AVLSDK_RPT_IDX_H__
#define	__AVLSDK_RPT_IDX_H__

#define RPT_ITEM_MAX_LIMIT                  (128)

#define RPT_IDX_OBJ_DESCRIPTION             (0)
#define RPT_IDX_MALWARE_ID                  (1)
#define RPT_IDX_ANALYSER                    (2)

#define RPT_IDX_FILE_FORMAT_ID              (3)
#define RPT_IDX_SHELL_ID                    (4)
#define RPT_IDX_INFECTED_ID                 (5)
#define RPT_IDX_COMM_DETECT_ID              (6)
#define RPT_IDX_PEP_DETECT_ID               (7)
#define RPT_IDX_EXPLOIT_DETECT_ID           (8)
#define RPT_IDX_SCRIPT_DETECT_ID            (9)
#define RPT_IDX_DC_COLLECT_INFO             (10)
#define RPT_IDX_PATH_FILTER_ID              (11)
#define RPT_IDX_EMC_ID                      (12)
#define RPT_IDX_VCS2S_DETECT_ID             (13)
#define RPT_IDX_VCS2S_DETAIL_LOW32          (14)
#define RPT_IDX_VCS2S_DETAIL_HIGH32         (15)
#define RPT_IDX_VCS2S_RESULT_DETAIL         (16)
#define RPT_IDX_VCS2S_THREAT_LEVEL          (17)
#define RPT_IDX_SEC_DETECT_ID               (18)
#define RPT_IDX_HEUR_FORMAT_ID              (19)
#define RPT_IDX_HD_DETECT_ID                (20)
#define RPT_IDX_STREAM_DETECT_ID            (21)
#define RPT_IDX_SFX_DETECT_ID               (22)
#define RPT_IDX_FBLOOM_DETECT_ID            (23)
#define RPT_IDX_KEXPLOIT_DETECT_ID          (24)
#define RPT_IDX_SUF_DETECT_ID               (25)
#define RPT_IDX_CLOUD_DETECT_ID             (26)
#define RPT_IDX_BOL_DETECT_ID               (27)
#define RPT_IDX_MSCRIPT_DETECT_ID           (28)
#define RPT_IDX_ELF_DETECT_ID               (29)
#define RPT_IDX_YARA_DETECT_ID              (30)
#define RPT_IDX_MACRO_DETECT_ID             (31)
#define RPT_IDX_ANDROID_DETECT_ID           (32)
#define RPT_IDX_GEN_HASH_DETECT_ID          (33)
#define RPT_IDX_VCS3S_DETECT_ID             (34)
#define RPT_IDX_NSIS_DETECT_ID              (35)
#define RPT_IDX_SWF_DETECT_ID               (36)
#define RPT_IDX_HEML_DETECT_ID              (37)
#define RPT_IDX_REG_DETECT_ID               (38)
#define RPT_IDX_HSCPT_DETECT_ID             (39)
#define RPT_IDX_ARCHIVE_DETECT_ID           (40)
#define RPT_IDX_GSCPT_DETECT_ID             (41)
#define RPT_IDX_DOH_DETECT_ID               (42)
#define RPT_IDX_DC_COLLECT_DETAIL           (43)
#define RPT_IDX_TXT_INFECTED_ID             (44)
#define RPT_IDX_VH_DETECT_ID                (45)
#define RPT_IDX_VC_DETECT_ID                (46)
#define RPT_IDX_ANDROID_EX_DETECT_ID        (47)
#define RPT_IDX_OBJ_MD5                     (48)
#define RPT_IDX_IMP_DETECT_ID               (49)
#define RPT_IDX_HMACRO_DETECT_ID            (50)
#define RPT_IDX_DC_DETECT_ID                (51)
#define RPT_IDX_DC_DETECT_DETAIL            (52)
#define RPT_IDX_FINFO_DETECT_ID             (53)

#define RPT_IDX_SKIPPED_REASON              (100)

#endif // __AVLSDK_RPT_IDX_H__


