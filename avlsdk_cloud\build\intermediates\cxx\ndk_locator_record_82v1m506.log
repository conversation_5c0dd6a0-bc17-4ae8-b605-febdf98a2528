[{"level_": 0, "message_": "android.ndkVersion from module build.gradle is [27.0.12077973]", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1622766544}, {"level_": 0, "message_": "android.ndkPath from module build.gradle is not set", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 871193039}, {"level_": 0, "message_": "ndk.dir in local.properties is not set", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1575654232}, {"level_": 0, "message_": "Not considering ANDROID_NDK_HOME because support was removed after deprecation period.", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -856498994}, {"level_": 0, "message_": "sdkFolder is D:\\Android\\Sdk", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1925558177}]