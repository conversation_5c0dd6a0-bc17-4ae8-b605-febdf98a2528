// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        maven {
            // url 'http://127.0.0.1:8081/repository/plugins.gradle.org/'
            url 'https://plugins.gradle.org/m2/'
        }
        maven {
            // url 'http://127.0.0.1:8081/repository/maven.google.com/'
            url 'https://maven.google.com/'
        }
    }
    dependencies {
        classpath "com.android.tools.build:gradle:7.0.4"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath 'org.aspectj:aspectjtools:1.8.9'
        classpath 'org.aspectj:aspectjweaver:1.8.9'
    }
}

allprojects {
    repositories {
        maven {
            // url 'http://127.0.0.1:8081/repository/maven-central/'
            url 'https://repo.maven.apache.org/maven2/'
        }
        maven {
            // url 'http://127.0.0.1:8081/repository/maven.google.com/'
            url 'https://maven.google.com'
        }
        maven {
            // url 'http://127.0.0.1:8081/repository/jcenter.bintray.com/'
            url 'https://jcenter.bintray.com/'
        }
        maven {
            // url 'http://127.0.0.1:8081/repository/jitpack.io/'
            url 'https://jitpack.io'
        }

        maven {
            //url 'http://127.0.0.1:8081/repository/maven.scijava.org/'
            url 'https://maven.scijava.org/'
        }

        mavenLocal()

        flatDir {
            dirs 'libs'
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
