package com.gkuw.szytq;

import android.app.Application;
import android.content.Context;
import android.os.Build;

import androidx.annotation.RequiresApi;

import com.antiy.avlsdk_cloud.util.MyDatabaseHelper;
import com.antiy.avlsdk_pc.AVLEnginePC;
import com.avl.engine.AVLEngine;

public class MyApplication extends Application {

    private static Context mContext;

    public static Context getGlobalContext() {
        return mContext;
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public void onCreate() {
        //移动引擎初始化
        AVLEngine.init(this);
        AVLEngine.setCloudScanEnabled(false);

        //PC引擎初始化
        AVLEnginePC.getInstance().prepareData(this);
        AVLEnginePC.getInstance().init();

        // 监控程序初始化
        CpuUsageMonitor.init(this);

        super.onCreate();
        mContext = getApplicationContext();
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
    }
}
