package com.antiy.avlsdk_cloud;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.antiy.avlsdk_cloud.util.FileMd5Utils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.List;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

/**
 * VirtualApp Native Project
 */
public class AVLEngineCloud {

    private static final String TAG = AVLEngineCloud.class.getSimpleName();
    private static boolean sFlag = false;

    private static AVLEngineCloud mInstance;
    private static String mAVLPath;

    public String getmAVLPath() {
        return mAVLPath;
    }

    static {
        try {
            System.loadLibrary("avlsdk_cloud");
        } catch (Throwable e) {
            Log.d(TAG, e.getMessage());
        }
    }

    private AVLEngineCloud() {
    }

    public static AVLEngineCloud getInstance() {
        if (mInstance == null) {
            synchronized (AVLEngineCloud.class) {
                if (mInstance == null) {
                    mInstance = new AVLEngineCloud();
                }
            }
        }

        return mInstance;
    }

    public static String getMD5Checksum(String filePath) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            FileInputStream fis = new FileInputStream(filePath);
            byte[] dataBytes = new byte[1024];
            int nread;
            while ((nread = fis.read(dataBytes)) != -1) {
                md5.update(dataBytes, 0, nread);
            }
            ;
            byte[] mdbytes = md5.digest();
            fis.close();
            StringBuilder hexString = new StringBuilder();
            for (byte mdbyte : mdbytes) {
                hexString.append(Integer.toString((mdbyte & 0xff) + 0x100, 16).substring(1));
            }
            return hexString.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getMD5(String input) {
        try {
            // Create MD5 Hash
            MessageDigest digest = java.security.MessageDigest.getInstance("MD5");
            digest.update(input.getBytes());
            byte messageDigest[] = digest.digest();

            // Create Hex String
            StringBuilder hexString = new StringBuilder();
            for (byte aMessageDigest : messageDigest) {
                String h = Integer.toHexString(0xFF & aMessageDigest);
                while (h.length() < 2) h = "0" + h;
                hexString.append(h);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static native String getApkInfo(String filePath);

    public static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sc = SSLContext.getInstance("TLS");
        // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(
                    java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                    String paramString) throws CertificateException {
            }

            @Override
            public void checkServerTrusted(
                    java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                    String paramString) throws CertificateException {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        sc.init(null, new TrustManager[]{trustManager}, null);
        return sc;
    }

    public List<String> getScanCloudResult(String response, int[] indexs) {
        List<String> indexRet = new ArrayList<>();
        try {
            JSONObject jsonObject = new JSONObject(response);
            JSONObject dataObject = (JSONObject) jsonObject.get("data");
            JSONObject result = dataObject.getJSONObject("result").getJSONObject("cert_hash");

            for (int i : indexs) {
                if (result.has("" + i)) {
                    indexRet.add(i, result.getString("" + i));
                } else {
                    indexRet.add(i, "未查到相关应用结果");
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return indexRet;
    }

    ///返回对应的值
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public List<String> scanAll(Context context, List<ApkInfo> apkInfos) {
        try {
            String appid = "4264547fe329bcceb9081534b91c2ad1";
            String secure_key = "248f4dd52261b747a692c5e8de88d73a";
            long ts = System.currentTimeMillis();
            String secret = getMD5(appid + ts + secure_key);
            //构造请求body
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("appid", "4264547fe329bcceb9081534b91c2ad1");
            jsonObject.put("time_stamp", ts);
            jsonObject.put("secret", secret);
            jsonObject.put("api_version", "3.0");
            JSONArray searchArray = new JSONArray();

            for (int i = 0; i < apkInfos.size(); i++) {
                ApkInfo apkInfo = apkInfos.get(i);

                File apkFile = new File(apkInfo.apkPath);
                PackageManager pm = context.getPackageManager();
                PackageInfo pkgInfo = pm.getPackageArchiveInfo(apkInfo.apkPath,
                        PackageManager.GET_META_DATA |
                                PackageManager.GET_ACTIVITIES |
                                PackageManager.GET_SERVICES |
                                PackageManager.GET_RECEIVERS |
                                PackageManager.GET_PROVIDERS);

                JSONObject searchInfo = new JSONObject();
                searchInfo.put("sign_block_hash", "");//没有这项
                searchInfo.put("key_set_hash", apkInfo.keyHash != null ? apkInfo.keyHash : "");
                searchInfo.put("mf_md5", apkInfo.mfHash != null ? apkInfo.mfHash : "");
                searchInfo.put("new_keyhash", apkInfo.newKeyHash != null ? apkInfo.newKeyHash : "");

                searchInfo.put("apk_size", "" + apkFile.length());
                searchInfo.put("apk_path", apkInfo.apkPath);
                searchInfo.put("apk_pkgname", pkgInfo != null ? pkgInfo.packageName : "");
                searchInfo.put("apk_md5", apkInfo.apkMd5);
                searchInfo.put("version_code", pkgInfo != null ? pkgInfo.versionCode + "" : "");
                searchInfo.put("program_name", pkgInfo != null ? pm.getApplicationLabel(pkgInfo.applicationInfo) : "");
                searchInfo.put("version_name", pkgInfo != null ? pkgInfo.versionName : "");
                searchInfo.put("fitim", "");
                searchInfo.put("lutim", "");
                searchInfo.put("index", "" + i);
                searchArray.put(searchInfo);

                jsonObject.put("search_info", searchArray);
            }

            Log.d(TAG, jsonObject.toString());

            // 目标URL
            String targetURL = "https://cse.avlyun.com/search/pro";
            // 创建URL对象
            URL url = new URL(targetURL);
            SSLContext sslContext = createIgnoreVerifySSL();
            // 打开连接
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setSSLSocketFactory(sslContext.getSocketFactory());

            // 设置请求方法为POST
            connection.setRequestMethod("POST");
            // 设置允许输入输出
            connection.setDoOutput(true);

            // 写入JSON数据
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonObject.toString().getBytes("UTF-8");
                os.write(input, 0, input.length);
            }

            // 获取响应码
            int responseCode = connection.getResponseCode();
            Log.d(TAG, "POST Response Code :: " + responseCode);

            StringBuilder response = new StringBuilder();
            // 根据需要处理响应数据
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 处理响应
                InputStream in = connection.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(in));

                String line;

                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }

                reader.close();
                // 输出响应内容
                Log.d(TAG, response.toString());
            }

            int[] indexs = new int[apkInfos.size()];
            for (int i = 0; i < indexs.length; i++) {
                indexs[i] = i;
            }
            // 关闭连接
            connection.disconnect();
            return getScanCloudResult(response.toString(), indexs);

        } catch (IOException | JSONException | NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }

        return null;
    }

    ///扫描某个文件，返回云扫描结果
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public String scan(Context context, String filePath, String apkInfoStr) {
        File apkFile = new File(filePath);
        if (!apkFile.exists()) {
            return null;
        }

        Log.d(TAG, "apkInfoStr " + apkInfoStr);

        String[] apkInfos = apkInfoStr.split(";");
        try {
            String appid = "4264547fe329bcceb9081534b91c2ad1";
            String secure_key = "248f4dd52261b747a692c5e8de88d73a";
            long ts = System.currentTimeMillis();
            String secret = getMD5(appid + ts + secure_key);

            PackageManager pm = context.getPackageManager();
            PackageInfo pkgInfo = pm.getPackageArchiveInfo(filePath,
                    PackageManager.GET_META_DATA |
                            PackageManager.GET_ACTIVITIES |
                            PackageManager.GET_SERVICES |
                            PackageManager.GET_RECEIVERS |
                            PackageManager.GET_PROVIDERS);
            //构造请求body
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("appid", "4264547fe329bcceb9081534b91c2ad1");
            jsonObject.put("time_stamp", ts);
            jsonObject.put("secret", secret);
            jsonObject.put("api_version", "3.0");

            JSONArray searchArray = new JSONArray();
            JSONObject searchInfo = new JSONObject();
            searchInfo.put("sign_block_hash", "");//没有这项
            searchInfo.put("key_set_hash", apkInfos != null && apkInfos.length > 0 ? apkInfos[0] : "");
            searchInfo.put("mf_md5", apkInfos != null && apkInfos.length > 1 ? apkInfos[1] : "");
            searchInfo.put("new_keyhash", apkInfos != null && apkInfos.length > 2 ? apkInfos[2] : "");

            searchInfo.put("apk_size", "" + apkFile.length());
            searchInfo.put("apk_path", filePath);
            searchInfo.put("apk_pkgname", pkgInfo != null ? pkgInfo.packageName : "");
            searchInfo.put("apk_md5", FileMd5Utils.getFileMd5(context, filePath));
            searchInfo.put("version_code", pkgInfo != null ? pkgInfo.versionCode + "" : "");
            searchInfo.put("program_name", pkgInfo != null ? pm.getApplicationLabel(pkgInfo.applicationInfo) : "");
            searchInfo.put("version_name", pkgInfo != null ? pkgInfo.versionName : "");
            searchInfo.put("fitim", "");
            searchInfo.put("lutim", "");
            searchInfo.put("index", "0");
            searchArray.put(searchInfo);

            jsonObject.put("search_info", searchArray);

            Log.d(TAG, jsonObject.toString());

            // 目标URL
            String targetURL = "https://cse.avlyun.com/search/pro";
            // 创建URL对象
            URL url = new URL(targetURL);
            SSLContext sslContext = createIgnoreVerifySSL();
            // 打开连接
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setSSLSocketFactory(sslContext.getSocketFactory());

            // 设置请求方法为POST
            connection.setRequestMethod("POST");
            // 设置允许输入输出
            connection.setDoOutput(true);

            // 写入JSON数据
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonObject.toString().getBytes("UTF-8");
                os.write(input, 0, input.length);
            }

            // 获取响应码
            int responseCode = connection.getResponseCode();
            Log.d(TAG, "POST Response Code :: " + responseCode);

            StringBuilder response = new StringBuilder();
            // 根据需要处理响应数据
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 处理响应
                InputStream in = connection.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(in));

                String line;

                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }

                reader.close();
                // 输出响应内容
                Log.d(TAG, response.toString());

            }
            // 关闭连接
            connection.disconnect();
            List<String> scanResult = getScanCloudResult(response.toString(), new int[]{0});

            return scanResult.size() > 0 ? scanResult.get(0) : "";
        } catch (IOException | JSONException | NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }

        return "";
    }
}

