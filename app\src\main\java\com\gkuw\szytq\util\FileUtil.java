package com.gkuw.szytq.util;

import android.os.Build;

import androidx.annotation.RequiresApi;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

public class FileUtil {
    static List<String> mMediaFormat = new ArrayList<>();

    static {
        mMediaFormat.add("audio/mpeg");
        mMediaFormat.add("video/mp4");
    }

    public static boolean isApkFile(File file) {
        if (file == null || !file.exists()) {
            return false;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] bytes = new byte[2];
            fis.read(bytes, 0, 2);
            return bytes[0] == 'P' && bytes[1] == 'K'; // "PK" in ASCII
        } catch (IOException e) {
            // <PERSON>le exception if necessary
            e.printStackTrace();
            return false;
        }
    }

    public static boolean isForMobile(File file) {
        if (file == null || !file.exists())
            return false;

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] bytes = new byte[3];
            fis.read(bytes, 0, 3);
            return (bytes[0] == 'P' && bytes[1] == 'K') || (bytes[0] == 'd' && bytes[1] == 'e' && bytes[2] == 'x');
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean isWordFile(File file) {
        return file.getAbsolutePath().endsWith(".doc") || file.getAbsolutePath().endsWith(".docx");
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private static String probeContentType(Path path) {
        try {
            return Files.probeContentType(path);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static boolean isMediaFile(File file) {
        return file.getAbsolutePath().endsWith(".mp3");
//        return FileFormatChecker.isMp4File(file.getAbsolutePath()) || file;
//        String mimeType = probeContentType(file.toPath());
//        Log.d("AVL", file.getAbsolutePath() + " " + mimeType);
//        for (String s : mMediaFormat) {
//            if (mimeType != null && mimeType.equalsIgnoreCase(s))
//                return true;
//        }
//
//        return false;
    }

    public static List<String> listAllFiles(File directory) {
        List<String> fileList = new ArrayList<String>();
        listFilesInDirectory(directory, fileList);
        return fileList;
    }

    private static void listFilesInDirectory(File directory, List<String> fileList) {
        if (directory != null && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null && files.length > 0) {
                for (File file : files) {
                    if (file.isFile()) {
                        fileList.add(file.getAbsolutePath());
                    } else if (file.isDirectory()) {
                        listFilesInDirectory(file, fileList);
                    }
                }
            }
        }
    }
}
