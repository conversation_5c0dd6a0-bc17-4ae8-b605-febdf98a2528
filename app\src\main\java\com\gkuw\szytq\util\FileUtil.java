package com.gkuw.szytq.util;

import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

public class FileUtil {
    private static final String TAG = "FileUtil";
    static List<String> mMediaFormat = new ArrayList<>();

    static {
        mMediaFormat.add("audio/mpeg");
        mMediaFormat.add("video/mp4");
    }

    public static boolean isApkFile(File file) {
        if (file == null || !file.exists()) {
            return false;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] bytes = new byte[2];
            fis.read(bytes, 0, 2);
            return bytes[0] == 'P' && bytes[1] == 'K'; // "PK" in ASCII
        } catch (IOException e) {
            // Handle exception if necessary
            e.printStackTrace();
            return false;
        }
    }

    public static boolean isForMobile(File file) {
        if (file == null || !file.exists())
            return false;

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] bytes = new byte[3];
            fis.read(bytes, 0, 3);
            return (bytes[0] == 'P' && bytes[1] == 'K') || (bytes[0] == 'd' && bytes[1] == 'e' && bytes[2] == 'x');
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean isWordFile(File file) {
        return file.getAbsolutePath().endsWith(".doc") || file.getAbsolutePath().endsWith(".docx");
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private static String probeContentType(Path path) {
        try {
            return Files.probeContentType(path);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static boolean isMediaFile(File file) {
        return file.getAbsolutePath().endsWith(".mp3");
//        return FileFormatChecker.isMp4File(file.getAbsolutePath()) || file;
//        String mimeType = probeContentType(file.toPath());
//        Log.d("AVL", file.getAbsolutePath() + " " + mimeType);
//        for (String s : mMediaFormat) {
//            if (mimeType != null && mimeType.equalsIgnoreCase(s))
//                return true;
//        }
//
//        return false;
    }

    public static List<String> listAllFiles(File directory) {
        List<String> fileList = new ArrayList<String>();
        Log.d(TAG, "开始扫描目录: " + (directory != null ? directory.getAbsolutePath() : "null"));

        if (directory == null) {
            Log.e(TAG, "目录参数为null");
            return fileList;
        }

        if (!directory.exists()) {
            Log.e(TAG, "目录不存在: " + directory.getAbsolutePath());
            return fileList;
        }

        if (!directory.isDirectory()) {
            Log.e(TAG, "路径不是目录: " + directory.getAbsolutePath());
            return fileList;
        }

        if (!directory.canRead()) {
            Log.e(TAG, "目录无法读取: " + directory.getAbsolutePath());
            return fileList;
        }

        listFilesInDirectory(directory, fileList);
        Log.d(TAG, "扫描完成，共找到 " + fileList.size() + " 个文件");
        return fileList;
    }

    private static void listFilesInDirectory(File directory, List<String> fileList) {
        if (directory != null && directory.isDirectory()) {
            Log.d(TAG, "正在扫描目录: " + directory.getAbsolutePath());

            File[] files = directory.listFiles();
            if (files == null) {
                Log.w(TAG, "无法列出目录内容 (可能是权限问题): " + directory.getAbsolutePath());
                return;
            }

            Log.d(TAG, "目录 " + directory.getAbsolutePath() + " 包含 " + files.length + " 个项目");

            if (files.length > 0) {
                for (File file : files) {
                    try {
                        if (file.isFile()) {
                            fileList.add(file.getAbsolutePath());
                            Log.v(TAG, "添加文件: " + file.getAbsolutePath());
                        } else if (file.isDirectory()) {
                            Log.d(TAG, "进入子目录: " + file.getAbsolutePath());
                            listFilesInDirectory(file, fileList);
                        }
                    } catch (SecurityException e) {
                        Log.w(TAG, "访问文件时权限不足: " + file.getAbsolutePath() + ", " + e.getMessage());
                    }
                }
            } else {
                Log.d(TAG, "目录为空: " + directory.getAbsolutePath());
            }
        } else {
            Log.w(TAG, "跳过非目录项: " + (directory != null ? directory.getAbsolutePath() : "null"));
        }
    }
}
