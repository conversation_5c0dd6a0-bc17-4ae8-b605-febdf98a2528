<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:contentDescription="扫描间隔"
    android:orientation="vertical"
    tools:ignore="HardcodedText">

    <LinearLayout
        android:id="@+id/linear_localscan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_scanDir"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始扫描"
            android:textSize="16sp" />


    </LinearLayout>

    <TextView
        android:id="@+id/editTextTextPersonName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20sp"
        android:ems="10"
        android:gravity="center_horizontal"
        android:inputType="textPersonName"
        android:text="切换类型"
        android:textSize="20sp" />

    <LinearLayout
        android:id="@+id/linear_cloud"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_setJar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="jar"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_setDex"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="dex"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_setApk"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="apk"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear_cloud_2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="visible">

        <Button
            android:id="@+id/btn_setMp3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="mp3"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_setMp4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="mp4"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_setJpg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="jpg"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear_cloud_3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="visible">

        <Button
            android:id="@+id/btn_setPng"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="png"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_setGif"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="gif"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_setElf"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="elf"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear_enginepc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="visible">

        <Button
            android:id="@+id/btn_setMultimedia"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="multimedia"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="扫描路径"
            android:textSize="18sp" />

        <EditText
            android:id="@+id/edt_scanDir"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:text="/sdcard/samples/jar" />
    </LinearLayout>

    <!--        <Button-->
    <!--            android:id="@+id/btn_scanCloudFile"-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_weight="1"-->
    <!--            android:selectAllOnFocus="false"-->
    <!--            android:text="云查杀"-->
    <!--            android:textSize="16sp" />-->

    <Switch
        android:id="@+id/toggleSleep"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6sp"
        android:text="扫描间隔" />

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/inputSleepMacroSec"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ems="10"
            android:inputType="number" />

        <TextView
            android:id="@+id/labelSleepMacroSec"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="us" />

        <Button
            android:id="@+id/btnSleepMicroSecConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确认"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btnSleepMacroSecReset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="重置"
            android:textSize="16sp" />
    </LinearLayout>

    <Switch
        android:id="@+id/toggleCpuLimit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8sp"
        android:text="CPULimit" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/inputPercent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ems="10"
            android:hint="percent, default 9"
            android:inputType="number"
            android:textSize="14sp" />

        <Button
            android:id="@+id/mBtnConfirmPercent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确认" />

        <EditText
            android:id="@+id/inputInterval"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ems="10"
            android:hint="interval, default 50"
            android:inputType="number"
            android:textSize="14sp" />

        <Button
            android:id="@+id/mBtnConfirmInterval"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确认" />
    </LinearLayout>

    <Button
        android:id="@+id/mBtnResetMonitor"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="重置"
        android:textSize="16sp" />

    <LinearLayout
        android:id="@+id/linear_infopanel"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_prompt"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>
</LinearLayout>
