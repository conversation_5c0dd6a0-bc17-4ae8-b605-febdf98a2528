package com.antiy.avlsdk_pc;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import java.io.File;

/**
 * VirtualApp Native Project
 */
public class AVLEnginePC {

    private static final String TAG = AVLEnginePC.class.getSimpleName();
    private static boolean sFlag = false;

    private static AVLEnginePC mInstance;
    private static String mAVLPath;

    public String getmAVLPath() {
        return mAVLPath;
    }

    static {
        try {
            System.loadLibrary("avlsdk_pc");
        } catch (Throwable e) {
            Log.d(TAG, e.getMessage());
        }
    }

    private AVLEnginePC() {
    }

    public static AVLEnginePC getInstance() {
        if (mInstance == null) {
            synchronized (AVLEnginePC.class) {
                if (mInstance == null) {
                    mInstance = new AVLEnginePC();
                }
            }
        }

        return mInstance;
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void prepareData(Context context) {
        //准备PC引擎需要的内容
        File avlsdk_pc = new File(context.getFilesDir().getAbsoluteFile() + "/avlsdk_pc");
        mAVLPath = avlsdk_pc.getAbsolutePath();

        if (!avlsdk_pc.exists()) {
            avlsdk_pc.mkdir();
        } else {
            return;
        }

        AssetsUtil.copyFilesFromAssets(context, "avlsdk_pc", context.getFilesDir() + "/avlsdk_pc");
    }

    public static int init() {
        return loadEngine(mAVLPath, Build.CPU_ABI);
    }

    //ret:
    //0: success, -1: 失败（后面再完善错误码）
    public static native int loadEngine(String sdk_dir, String abi);

    public static native String scan(String filePath);

    public static native String getDbInfo();
}
