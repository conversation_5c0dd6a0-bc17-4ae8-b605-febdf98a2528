/************************************************************************\
							FILE INFORMATION
			==================================================
			Creator			:	PieroLsl <<EMAIL>>
			Create time		:	2013-06-07 11:35:33
			Company			:	Antiy Labs  (Harbin, China)
			Location		:	Harbin, China

									LEGAL
			==================================================
			Antiy Labs All rights reserved
  
							FILE DESCRIPTION
			==================================================
			The demonstration code, shows out the way of how to 
			use the APIs of AVLSDK3.0.
			TAB = 4 space
\************************************************************************/

#include <sys/mman.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/stat.h>
#include <unistd.h>
#include <errno.h>
#include <stdio.h>
#include <string.h>
#include <getopt.h>
#include <dlfcn.h>
#include <malloc.h>

#include "engine.h"
#include "error_code.h"
#include "AVLSDK_rpt_idx.h"
#include "AVLSDK_conf_idx.h"
#include "ID2Name_interface.h"

#include "jni.h"
#include "vlog.h"

#define    DEFAULT_SDK_PATH        ("AVLSDK.so")
#define    DEFAULT_ID2NAME_PATH    ("aid2name.so")
#define    DEFAULT_ID2N_DB_PATH    ("NData")
#define    DEFAULT_LOAD_CONFIG     ("Config/high_scan.ct")

typedef long    (*P_AVL_SDK_Create)(void **pEngine);

typedef long    (*P_AVL_SDK_Release)(void *pEngine);

typedef long    (*P_AVL_SDK_LoadConfigFile)(void *pEngine, char *szFilename);

typedef long    (*P_AVL_SDK_Init)(void *pEngine, const void *pVerificationCode);

typedef long    (*P_AVL_SDK_SetConfigInt)(void *pEngine, long CfgIdx, long lValue);

typedef long    (*P_AVL_SDK_SetConfigString)(void *pEngine, long CfgIdx, const char *pValue);

typedef long    (*P_AVL_SDK_Scan)(void *pEngine, P_OBJ_PROVIDER pObj, P_OBJ_DISPOSER pObjDisposer);

typedef long    (*P_AVL_SDK_QueryReportInt)(void *pEngine, void *pRptHandle, unsigned long key,
                                            long *value);

typedef long    (*P_AVL_SDK_QueryReportStr)(void *pEngine, void *pRptHandle, unsigned long key,
                                            unsigned char **value);

typedef long    (*P_AVL_SDK_QueryDBInfo)(void *pEngine, P_DB_INFO pDBInfo);

typedef long    (*P_AVL_SDK_ReloadDB)(void *pEngine);

typedef long    (*P_AVL_NTranser_Init)(const char *path, void **handle);

typedef long    (*P_AVL_NTranser_QueryNameByID)(void *handle, const char *mod_name, long id,
                                                unsigned char *buf, unsigned long size);

typedef void    (*P_AVL_NTranser_Release)(void *handle);

typedef struct _engine_param {
    void *p_engine;
    void *p_i2n_handle;
    P_AVL_SDK_QueryReportInt p_query_rpt_int;
    P_AVL_SDK_QueryReportStr p_query_rpt_str;
    P_AVL_NTranser_QueryNameByID p_query_name;
} ENGINE_PARAM, *P_ENGINE_PARAM;

long func_long_query_continue_callback(void *p_param) {
    // This is the code sample, so it returns unconditionally. Users can modify according to the condition.
    return OD_CONTINUE;
}

long func_long_get_rslt_callback(P_OBJ_PROVIDER p_op, void *p_data, void *p_param) {
    long long_ret = 0, long_malware_id = 0, long_qry_ret = 0;
    P_ENGINE_PARAM p_ep = (P_ENGINE_PARAM) p_param;
    unsigned char *puchar_desc = NULL, *puchar_analyser = NULL, auchar_malware_name[128] = {0};

    if (p_data == NULL || p_param == NULL) {
        long_ret = -1;
        goto GRC_OUT;
    }

    // Query the Malware ID
    long_qry_ret = p_ep->p_query_rpt_int(p_ep->p_engine, p_data, RPT_IDX_MALWARE_ID,
                                         &long_malware_id);
    if (long_qry_ret == ERR_RPT_NOT_EXIST) {
        goto GRC_OUT;
    } else if (long_qry_ret < 0) {
        LOGD("Query Malware_ID failed...", "");
        goto GRC_OUT;
    }

    // Query the analyser who detected this malware, users will use it when they need to get the malware name
    long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_ANALYSER,
                                         &puchar_analyser);
    if (long_qry_ret != ERR_SUCCESS) {
        printf("Query Analyser failed...\n");
        LOGD("Query Analyser failed...", "");
        goto GRC_OUT;
    }

    // Query the VName
    long_qry_ret = p_ep->p_query_name(p_ep->p_i2n_handle, (char *) puchar_analyser, long_malware_id,
                                      (char *) auchar_malware_name,
                                      sizeof(auchar_malware_name) - 1);
    if (long_qry_ret < 0) {
        printf("Query Malware_Name failed...\n");
        LOGD("Query Malware_Name failed...", "");
        goto GRC_OUT;
    }

    // Query current description about the object
    long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_OBJ_DESCRIPTION,
                                         &puchar_desc);
    if (long_qry_ret != ERR_SUCCESS) {
        printf("Query Desc failed...\n");
        LOGD("Query Desc failed...", "");
        goto GRC_OUT;
    }

    printf("Found malware : %x\t\t%s\t\t%s\t\t%s\n", long_malware_id, (char *) auchar_malware_name,
           (char *) puchar_analyser, (char *) puchar_desc);

    LOGD("Found malware : %x\t\t%s\t\t%s\t\t%s", long_malware_id, (char *) auchar_malware_name,
           (char *) puchar_analyser, (char *) puchar_desc);

    GRC_OUT:
    return long_ret;
}

void func_void_show_usage() {
    printf("\t\t--file   -f : scan the path of target file.\n");
    printf("\t\t--conf   -c : config file.\n");
    printf("\t\t--help   -h : show this message.\n");
}

static const struct option options[] =
        {
                {"file", required_argument, NULL, 'f'},            //	扫描文件
                {"conf", required_argument, NULL, 'c'},            //	配置文件
                {"help", no_argument,       NULL, 'h'},            //	帮助
                {NULL, 0,                   NULL, 0}
        };

int main(int argc, char *argv[]) {
    long long_last_ret = 0;
    void *p_engine_handle = NULL, *p_i2n_handle = NULL;
    void *p_sdk_handle = NULL, *p_name_handle = NULL;
    P_AVL_SDK_Create p_create = NULL;
    P_AVL_SDK_Release p_release = NULL;
    P_AVL_SDK_Init p_init = NULL;
    P_AVL_SDK_LoadConfigFile p_config = NULL;
    P_AVL_SDK_SetConfigInt p_set_cfg_int = NULL;
    P_AVL_SDK_SetConfigString p_set_cfg_str = NULL;
    P_AVL_SDK_Scan p_scan = NULL;
    P_AVL_SDK_QueryReportInt p_query_rpt_int = NULL;
    P_AVL_SDK_QueryReportStr p_query_rpt_str = NULL;
    P_AVL_SDK_ReloadDB p_reload = NULL;
    P_AVL_SDK_QueryDBInfo p_query_db_info = NULL;
    P_AVL_NTranser_Init p_i2n_init = NULL;
    P_AVL_NTranser_Release p_i2n_release = NULL;
    P_AVL_NTranser_QueryNameByID p_i2n_query_name = NULL;
    char *pchar_scan_file = NULL, *pchar_conf_file = NULL;
    int int_c = 0, i = 0;

    printf("\t\tAGB for consol v1.0.1.1 by Antiy Labs\n");

    while ((int_c = getopt_long(argc, argv, "c:hf:", options, NULL)) >= 0) {
        switch (int_c) {
            case 'f':
                pchar_scan_file = optarg;
                break;
            case 'c':
                pchar_conf_file = optarg;
                break;
            case 'h':
                func_void_show_usage();
                return 0;
        }
    }

    if (pchar_conf_file == NULL) {
        goto MAIN_OUT;
    }

    // Load the SDK so
    p_sdk_handle = dlopen((const unsigned char *) DEFAULT_SDK_PATH, RTLD_LAZY);
    if (p_sdk_handle == NULL) {
        printf("Load SDK failed...\n");
        goto MAIN_OUT;
    }

    // Load the NameTranser so
    p_name_handle = dlopen((const unsigned char *) DEFAULT_ID2NAME_PATH, RTLD_LAZY);
    if (p_name_handle == NULL) {
        printf("Load SDK failed...\n");
        goto MAIN_OUT;
    }

    // Get APIs
    p_create = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_CreateInstance");
    p_release = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_Release");
    p_init = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_InitInstance");
    p_config = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_LoadConfigFile");
    p_set_cfg_int = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_SetConfigInt");
    p_set_cfg_str = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_SetConfigString");
    p_scan = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_Scan");
    p_query_rpt_int = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_QueryReportInt");
    p_query_rpt_str = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_QueryReportStr");
    p_reload = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_ReloadDB");
    p_query_db_info = dlsym(p_sdk_handle, (const unsigned char*)"AVL_SDK_QueryDBInfo");

    if (p_create == NULL || p_release == NULL || p_init == NULL || p_config == NULL ||
        p_set_cfg_int == NULL || p_set_cfg_str == NULL || p_scan == NULL ||
        p_query_rpt_int == NULL || p_query_rpt_str == NULL || p_reload == NULL) {
        printf("Get procs failed...\n");
        goto MAIN_OUT;
    }

    p_i2n_init = dlsym(p_name_handle, (const unsigned char *) "AVL_NTranser_Init");
    p_i2n_release = dlsym(p_name_handle, (const unsigned char *) "AVL_NTranser_Release");
    p_i2n_query_name = dlsym(p_name_handle, (const unsigned char *) "AVL_NTranser_QueryNameByID");

    if (p_i2n_init == NULL || p_i2n_release == NULL || p_i2n_query_name == NULL) {
        printf("Get procs failed...\n");
        goto MAIN_OUT;
    }

    START:
    // Create a new instance of AVLSDK
    long_last_ret = p_create(&p_engine_handle);
    if (long_last_ret != ERR_SUCCESS) {
        printf("Create failed : %d\n", (int) long_last_ret);
        goto MAIN_OUT;
    }

    // Load the configuration template
    long_last_ret = p_config(p_engine_handle, pchar_conf_file);
    if (long_last_ret != ERR_SUCCESS) {
        printf("LoadConfig failed : %d\n", (int) long_last_ret);
        goto MAIN_OUT;
    }

    // Set the configuration by users
    long_last_ret = p_set_cfg_int(p_engine_handle, CFG_INT_APACK_RECURE_LAYER, 5);
    if (long_last_ret != ERR_SUCCESS) {
        printf("SetConfig failed : %d\n", (int) long_last_ret);
        goto MAIN_OUT;
    }

    // Set the configuration by users
    long_last_ret = p_set_cfg_str(p_engine_handle, CFG_STR_LICENSE_PATH, "./License.alf");
    if (long_last_ret != ERR_SUCCESS) {
        printf("SetConfig failed : %d\n", (int) long_last_ret);
        goto MAIN_OUT;
    }

    // Initialize the instance
    long_last_ret = p_init(p_engine_handle, NULL);
    if (long_last_ret != ERR_SUCCESS) {
        printf("Init failed : %d\n", (int) long_last_ret);
        p_engine_handle = NULL;
        goto MAIN_OUT;
    }

    // Initialize the NameTranser instance
    long_last_ret = p_i2n_init(DEFAULT_ID2N_DB_PATH, &p_i2n_handle);
    if (long_last_ret != 0) {
        printf("Init NameTranser failed : %d\n", (int) long_last_ret);
        goto MAIN_OUT;
    }

    SCAN:
    // Scan
    if (pchar_scan_file != NULL) {
        unsigned char *puchar_buf = NULL;
        unsigned long ulong_file_size = 0;
        int fd = 0;
        struct stat status = {0};
        OBJ_PROVIDER op = {0};
        OBJ_DISPOSER od = {0};
        ENGINE_PARAM ep = {0};

        fd = open(pchar_scan_file, O_RDONLY);
        if (fd != -1) {
            lstat(pchar_scan_file, &status);
            ulong_file_size = status.st_size;
            puchar_buf = (unsigned char *) mmap(NULL, ulong_file_size, PROT_READ, MAP_SHARED, fd,
                                                0);
        }
        printf("Scan %s...\n", pchar_scan_file);

        // Initialize the OBJ_PROVIDER structure
        op.obj_ver = CUR_ENGINE_VER;
        op.evro_type = ET_DESKTOP;
        op.buf = puchar_buf;
        op.size = ulong_file_size;
        strncpy((char *) op.obj_des, pchar_scan_file, sizeof(op.obj_des));

        // Initialize the ENGINE_PARAM structure, it will be used by report callback
        ep.p_engine = p_engine_handle;
        ep.p_i2n_handle = p_i2n_handle;
        ep.p_query_rpt_int = p_query_rpt_int;
        ep.p_query_rpt_str = p_query_rpt_str;
        ep.p_query_name = p_i2n_query_name;

        // Initialize the OBJ_DISPOSERR structure
        od.rpt_callback = func_long_get_rslt_callback;
        od.p_rpt_param = &ep;
        od.query_continue_callback = func_long_query_continue_callback;
        od.p_qc_param = NULL;

        long_last_ret = p_scan(p_engine_handle, &op, &od);
        if (long_last_ret < 0) {
            printf("Scan failed...\n");
        }

        if (puchar_buf != NULL) {
            munmap(puchar_buf, ulong_file_size);
            puchar_buf = NULL;
        }

        if (fd != -1) {
            close(fd);
        }
    }

#if 0
    for (; i < 3; i++)
    {
        if (p_reload(p_engine_handle) != ERR_SUCCESS)
        {
            printf("Reload failed...\n");
        }
        else
        {
            i++;
            goto	SCAN;
        }
    }
#endif

    // Release the AVLSDK instance and the so...
    MAIN_OUT:
    if (p_engine_handle != NULL) {
        p_release(p_engine_handle);
        p_engine_handle = NULL;
    }

    if (p_i2n_handle != NULL) {
        p_i2n_release(p_i2n_handle);
        p_i2n_handle = NULL;
    }

#if 0
    for (; i < 3000; i++)
    {
        printf("goto %d\n", i);
        i++;
        getchar();
        goto	START;
    }
#endif

    if (p_sdk_handle != NULL) {
        dlclose(p_sdk_handle);
    }

    if (p_name_handle != NULL) {
        dlclose(p_name_handle);
    }


    return 0;
}

static int __attribute__ ((visibility("hidden"))) jniRegisterNativeMethods(JNIEnv* env, jclass cls, const JNINativeMethod* gMethods, int numMethods)
{
    if ( cls == NULL ) {
        return -1;
    }

    int result = 0;
    if ( (*env)->RegisterNatives(env, cls, gMethods, numMethods) < 0) {
        result = -1;
    }

    (*env)->DeleteLocalRef(env, cls);
    return result;
}

typedef struct ScanResult {
    char malwareName[128];
    char description[256];
}ScanResult;

typedef struct EngineHandler {
    void *pengine_handle;

    void *p_i2n_handle;

    P_AVL_SDK_Create p_create;
    P_AVL_SDK_Release p_release;
    P_AVL_SDK_Init p_init;
    P_AVL_SDK_LoadConfigFile p_config;
    P_AVL_SDK_SetConfigInt p_set_cfg_int;
    P_AVL_SDK_SetConfigString p_set_cfg_str;
    P_AVL_SDK_Scan p_scan;
    P_AVL_SDK_QueryReportInt p_query_rpt_int;
    P_AVL_SDK_QueryReportStr p_query_rpt_str;
    P_AVL_SDK_ReloadDB p_reload;
    P_AVL_SDK_QueryDBInfo p_query_db_info;
    P_AVL_NTranser_Init p_i2n_init;
    P_AVL_NTranser_Release p_i2n_release;
    P_AVL_NTranser_QueryNameByID p_i2n_query_name;

    ScanResult pScanResult;
}EngineHandler;

static EngineHandler mEngineHandler;

long my_func_long_get_rslt_callback(P_OBJ_PROVIDER p_op, void *p_data, void *p_param) {
    long long_ret = 0, long_malware_id = 0, long_qry_ret = 0;
    P_ENGINE_PARAM p_ep = (P_ENGINE_PARAM) p_param;
    unsigned char *puchar_desc = NULL, *puchar_analyser = NULL, auchar_malware_name[128] = {0};

    if (p_data == NULL || p_param == NULL) {
        long_ret = -1;
        goto GRC_OUT;
    }

    // Query the Malware ID
    long_qry_ret = p_ep->p_query_rpt_int(p_ep->p_engine, p_data, RPT_IDX_MALWARE_ID,
                                         &long_malware_id);
    if (long_qry_ret == ERR_RPT_NOT_EXIST) {
        goto GRC_OUT;
    } else if (long_qry_ret < 0) {
        LOGD("Query Malware_ID failed...", "");
        goto GRC_OUT;
    }

    // Query the analyser who detected this malware, users will use it when they need to get the malware name
    long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_ANALYSER,
                                         &puchar_analyser);
    if (long_qry_ret != ERR_SUCCESS) {
        printf("Query Analyser failed...\n");
        LOGD("Query Analyser failed...", "");
        goto GRC_OUT;
    }

    // Query the VName
    long_qry_ret = p_ep->p_query_name(p_ep->p_i2n_handle, (char *) puchar_analyser, long_malware_id,
                                      (char *) auchar_malware_name,
                                      sizeof(auchar_malware_name) - 1);
    if (long_qry_ret < 0) {
        printf("Query Malware_Name failed...\n");
        LOGD("Query Malware_Name failed...", "");
        goto GRC_OUT;
    }

    // Query current description about the object
    long_qry_ret = p_ep->p_query_rpt_str(p_ep->p_engine, p_data, RPT_IDX_OBJ_DESCRIPTION,
                                         &puchar_desc);
    if (long_qry_ret != ERR_SUCCESS) {
        printf("Query Desc failed...\n");
        LOGD("Query Desc failed...", "");
        goto GRC_OUT;
    }

    printf("Found malware : %x\t\t%s\t\t%s\t\t%s\n", long_malware_id, (char *) auchar_malware_name,
           (char *) puchar_analyser, (char *) puchar_desc);

    LOGD("Found malware : %x\t\t%s\t\t%s\t\t%s", long_malware_id, (char *) auchar_malware_name,
         (char *) puchar_analyser, (char *) puchar_desc);

    strcpy(mEngineHandler.pScanResult.malwareName, auchar_malware_name);

    GRC_OUT:
    return long_ret;
}

// get db version
static jstring getDbInfo(JNIEnv *env, jclass type){
    if(mEngineHandler.pengine_handle==NULL){return NULL;};

    P_DB_INFO info;
    mEngineHandler.p_query_db_info(mEngineHandler.pengine_handle, info);

    LOGD("Current db ver: %s", info->db_time_stamp);

    jstring result = (*env)->NewStringUTF(env, info->db_time_stamp);

    return result;
}

static jstring scanFile(JNIEnv *env, jclass type, jstring filePath) {
    long long_last_ret = 0;
    char *pchar_scan_file = NULL;

    if (mEngineHandler.pengine_handle == NULL) {
        return NULL;
    }
    pchar_scan_file = (*env)->GetStringUTFChars(env, filePath, 0);

    mEngineHandler.pScanResult.malwareName[0] = '\0';
    mEngineHandler.pScanResult.description[0] = '\0';

    if (pchar_scan_file != NULL) {
        unsigned char *puchar_buf = NULL;
        unsigned long ulong_file_size = 0;
        int fd = 0;
        struct stat status = {0};
        OBJ_PROVIDER op = {0};
        OBJ_DISPOSER od = {0};
        ENGINE_PARAM ep = {0};

        fd = open(pchar_scan_file, O_RDONLY);
        if (fd != -1) {
            lstat(pchar_scan_file, &status);
            ulong_file_size = status.st_size;
            puchar_buf = (unsigned char *) mmap(NULL, ulong_file_size, PROT_READ, MAP_SHARED, fd,
                                                0);
        }
        LOGD("Scan %s...", pchar_scan_file);

        // Initialize the OBJ_PROVIDER structure
        op.obj_ver = CUR_ENGINE_VER;
        op.evro_type = ET_DESKTOP;
        op.buf = puchar_buf;
        op.size = ulong_file_size;
        strncpy((char *) op.obj_des, pchar_scan_file, sizeof(op.obj_des));

        // Initialize the ENGINE_PARAM structure, it will be used by report callback
        ep.p_engine = mEngineHandler.pengine_handle;
        ep.p_i2n_handle = mEngineHandler.p_i2n_handle;
        ep.p_query_rpt_int = mEngineHandler.p_query_rpt_int;
        ep.p_query_rpt_str = mEngineHandler.p_query_rpt_str;
        ep.p_query_name = mEngineHandler.p_i2n_query_name;

        // Initialize the OBJ_DISPOSERR structure
        od.rpt_callback = my_func_long_get_rslt_callback;
        od.p_rpt_param = &ep;
        od.query_continue_callback = func_long_query_continue_callback;
        od.p_qc_param = NULL;

        long_last_ret = mEngineHandler.p_scan(mEngineHandler.pengine_handle, &op, &od);
        if (long_last_ret < 0) {
            LOGD("Scan failed %d", long_last_ret);
        }

        LOGD("Scan ok", "");
        if (puchar_buf != NULL) {
            munmap(puchar_buf, ulong_file_size);
            puchar_buf = NULL;
        }

        if (fd != -1) {
            close(fd);
        }
    }

    //根据全局引擎中保存的扫描结果，填充内容。
    LOGD("size %d\n", sizeof(ScanResult));
    char *tmp = malloc(sizeof(ScanResult) + 2 + 1);
    sprintf(tmp, "%s", mEngineHandler.pScanResult.malwareName);
    jstring result = (*env)->NewStringUTF(env, tmp);
    free(tmp);

    LOGD("get result done.", "");
    return result;
}

static int loadEngine(JNIEnv *env, jclass type, jstring filesDir, jstring abi) {
    long long_last_ret = 0;
    void *p_sdk_handle = NULL, *p_name_handle = NULL;

    mEngineHandler.p_create = NULL;
    mEngineHandler.p_release = NULL;
    mEngineHandler.p_init = NULL;
    mEngineHandler.p_config = NULL;
    mEngineHandler.p_set_cfg_int = NULL;
    mEngineHandler.p_set_cfg_str = NULL;
    mEngineHandler.p_scan = NULL;
    mEngineHandler.p_query_rpt_int = NULL;
    mEngineHandler.p_query_rpt_str = NULL;
    mEngineHandler.p_reload = NULL;
    mEngineHandler.p_i2n_init = NULL;
    mEngineHandler.p_i2n_release = NULL;
    mEngineHandler.p_i2n_query_name = NULL;
    mEngineHandler.p_i2n_handle = NULL;

    memset(mEngineHandler.pScanResult.malwareName, 0, sizeof(mEngineHandler.pScanResult.malwareName));
    memset(mEngineHandler.pScanResult.description, 0, sizeof(mEngineHandler.pScanResult.description));

//    P_AVL_SDK_Create p_create = NULL;
//    P_AVL_SDK_Release p_release = NULL;
//    P_AVL_SDK_Init p_init = NULL;
//    P_AVL_SDK_LoadConfigFile p_config = NULL;
//    P_AVL_SDK_SetConfigInt p_set_cfg_int = NULL;
//    P_AVL_SDK_SetConfigString p_set_cfg_str = NULL;
//    P_AVL_SDK_Scan p_scan = NULL;
//    P_AVL_SDK_QueryReportInt p_query_rpt_int = NULL;
//    P_AVL_SDK_QueryReportStr p_query_rpt_str = NULL;
//    P_AVL_SDK_ReloadDB p_reload = NULL;
//    P_AVL_NTranser_Init p_i2n_init = NULL;
//    P_AVL_NTranser_Release p_i2n_release = NULL;
//    P_AVL_NTranser_QueryNameByID p_i2n_query_name = NULL;

    char *pchar_conf_file = NULL;
    char *pchar_abi = NULL;
    int int_c = 0, i = 0;

    pchar_conf_file = (*env)->GetStringUTFChars(env, filesDir, 0);
    pchar_abi = (*env)->GetStringUTFChars(env, abi, 0);

    char fileBuf[256] = {0,};
    sprintf(fileBuf, "%s/%s", pchar_conf_file, DEFAULT_LOAD_CONFIG);

    char avl_sdk_path[256] = {0,};
    sprintf(avl_sdk_path, "%s/%s/%s", pchar_conf_file, pchar_abi, DEFAULT_SDK_PATH);
    LOGD("AVLSDK.so path: %s", avl_sdk_path);

    char avl_id2name_path[256] = {0,};
    sprintf(avl_id2name_path, "%s/%s/%s", pchar_conf_file, pchar_abi, DEFAULT_ID2NAME_PATH);
    LOGD("id2name.so path: %s", avl_id2name_path);

//    LOGD("\t\tAGB for consol v1.0.1.1 by Antiy Labs\n", "")
    // Load the SDK so
    p_sdk_handle = dlopen((const unsigned char *) avl_sdk_path, RTLD_LAZY);
    if (p_sdk_handle == NULL) {
        LOGD("Load SDK failed... %s\n", dlerror());
        return -1;
    }

    // Load the NameTranser so
    p_name_handle = dlopen((const unsigned char *) avl_id2name_path, RTLD_LAZY);
    if (p_name_handle == NULL) {
        LOGD("Load SDK failed...\n", "");
        return -1;
    }

    // Get APIs
    mEngineHandler.p_create = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_CreateInstance");
    mEngineHandler.p_release = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_Release");
    mEngineHandler.p_init = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_InitInstance");
    mEngineHandler.p_config = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_LoadConfigFile");
    mEngineHandler.p_set_cfg_int = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_SetConfigInt");
    mEngineHandler.p_set_cfg_str = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_SetConfigString");
    mEngineHandler.p_scan = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_Scan");
    mEngineHandler.p_query_rpt_int = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_QueryReportInt");
    mEngineHandler.p_query_rpt_str = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_QueryReportStr");
    mEngineHandler.p_reload = dlsym(p_sdk_handle, (const unsigned char *) "AVL_SDK_ReloadDB");
    mEngineHandler.p_query_db_info = dlsym(p_sdk_handle, (const unsigned char*)"AVL_SDK_QueryDBInfo");


    if (mEngineHandler.p_create == NULL || mEngineHandler.p_release == NULL || mEngineHandler.p_init == NULL || mEngineHandler.p_config == NULL ||
            mEngineHandler.p_set_cfg_int == NULL || mEngineHandler.p_set_cfg_str == NULL || mEngineHandler.p_scan == NULL ||
            mEngineHandler.p_query_rpt_int == NULL || mEngineHandler.p_query_rpt_str == NULL || mEngineHandler.p_reload == NULL || mEngineHandler.p_query_db_info == NULL) {
        LOGD("Get procs failed...\n", "");
        return -1;
    }

    mEngineHandler.p_i2n_init = dlsym(p_name_handle, (const unsigned char *) "AVL_NTranser_Init");
    mEngineHandler.p_i2n_release = dlsym(p_name_handle, (const unsigned char *) "AVL_NTranser_Release");
    mEngineHandler.p_i2n_query_name = dlsym(p_name_handle, (const unsigned char *) "AVL_NTranser_QueryNameByID");

    if (mEngineHandler.p_i2n_init == NULL || mEngineHandler.p_i2n_release == NULL || mEngineHandler.p_i2n_query_name == NULL) {
        LOGD("Get procs failed...\n", "");
        return -1;
    }

    START:
    // Create a new instance of AVLSDK
    long_last_ret = mEngineHandler.p_create(&mEngineHandler.pengine_handle);
    if (long_last_ret != ERR_SUCCESS) {
        LOGD("Create failed : %d\n", (int) long_last_ret);
        return -1;
    }

    // Load the configuration template
    long_last_ret = mEngineHandler.p_config(mEngineHandler.pengine_handle, fileBuf);
    if (long_last_ret != ERR_SUCCESS) {
        LOGD("LoadConfig failed : %d\n", (int) long_last_ret);
        return -1;
    }

    // 开启日志调试
    mEngineHandler.p_set_cfg_int(mEngineHandler.pengine_handle, CFG_FLAG_LOG_ENABLE, 1);
    mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, CFG_STR_LOG_PATH, pchar_conf_file);

    memset(fileBuf, 0, sizeof(fileBuf));
    sprintf(fileBuf, "%s/%s", pchar_conf_file, "Data");
    mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, CFG_STR_DATA_PATH, fileBuf);

    memset(fileBuf, 0, sizeof(fileBuf));
    sprintf(fileBuf, "%s/%s", pchar_conf_file, "Dam");
    LOGD("Dam path %s", fileBuf);
    mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, 316, fileBuf);

    memset(fileBuf, 0, sizeof(fileBuf));
    sprintf(fileBuf, "%s/%s", pchar_conf_file, "Module");
    LOGD("module path %s", fileBuf);
    mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, CFG_STR_MODULE_PATH, fileBuf);

    // Set the configuration by users
    long_last_ret = mEngineHandler.p_set_cfg_int(mEngineHandler.pengine_handle, CFG_INT_APACK_RECURE_LAYER, 5);
    if (long_last_ret != ERR_SUCCESS) {
        LOGD("SetConfig failed : %d\n", (int) long_last_ret);
        return -1;
    }

    // Set the configuration by users
    memset(fileBuf, 0, sizeof(fileBuf));
    sprintf(fileBuf, "%s/%s", pchar_conf_file, "License.alf");
    LOGD("license file %s", fileBuf);
//    long_last_ret = p_set_cfg_str(p_engine_handle, CFG_STR_LICENSE_PATH, "License.alf");
    long_last_ret = mEngineHandler.p_set_cfg_str(mEngineHandler.pengine_handle, CFG_STR_LICENSE_PATH, fileBuf);
    if (long_last_ret != ERR_SUCCESS) {
        LOGD("SetConfig failed : %d\n", (int) long_last_ret);
        return -1;
    }

    // Initialize the instance
    long_last_ret = mEngineHandler.p_init(mEngineHandler.pengine_handle, NULL);
    if (long_last_ret != ERR_SUCCESS) {
        LOGD("Init failed : %d\n", (int) long_last_ret);
        mEngineHandler.pengine_handle = NULL;
        return -1;
    }

    // Initialize the NameTranser instance
    memset(fileBuf, 0, sizeof(fileBuf));
    sprintf(fileBuf, "%s/%s", pchar_conf_file, DEFAULT_ID2N_DB_PATH);
    LOGD("NData path %s", fileBuf);
    long_last_ret = mEngineHandler.p_i2n_init(fileBuf, &mEngineHandler.p_i2n_handle);
    if (long_last_ret != 0) {
        LOGD("Init NameTranser failed : %d\n", (int) long_last_ret);
        return -1;
    }

    LOGD("初始化成功", "");
    return 0;
}

static JNINativeMethod jni_Methods_table[] = {
        { "loadEngine", "(Ljava/lang/String;Ljava/lang/String;)I", (void *) loadEngine },
        {"scan", "(Ljava/lang/String;)Ljava/lang/String;", (void *) scanFile},
        {"getDbInfo", "()Ljava/lang/String;", (void *)getDbInfo},
};

JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *reserved) {
    JNIEnv *env;
    if ((*vm)->GetEnv(vm, (void **) &env, JNI_VERSION_1_4) != JNI_OK) {
        return -1;
    }

    jclass cls = (*env)->FindClass(env, "com/antiy/avlsdk_pc/AVLEnginePC");
    jniRegisterNativeMethods(env, cls, jni_Methods_table,
                             sizeof(jni_Methods_table) / sizeof(JNINativeMethod));

    LOGD("HELLO%s", "");

    return JNI_VERSION_1_4;
}


