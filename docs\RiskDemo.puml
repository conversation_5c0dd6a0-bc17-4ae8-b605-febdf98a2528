@startwbs 组件图
* APP
** 界面
*** 开始扫描
*** 设置扫描路径
*** 设置扫描参数
*** 显示扫描进度
** 封装层
*** SDK的初始化
*** 扫描单文件
*** 扫描目录
** 移动端SDK
*** 初始化
*** 更新
*** 扫描文件
** PC端SDK
*** 初始化
*** 更新
*** 扫描文件
** 其他依赖
*** 权限申请
@endwbs

@startuml 用例图

skinparam Linetype polyline
left to right direction

actor User
actor App

rectangle 测试过程 {
        usecase 设置扫描参数 as setArgs
        usecase 选择文件or目录 as selectFile
        usecase 开始扫描 as scanFile
}

rectangle 统计过程 {
        usecase 统计扫描信息 as statistics
        usecase 导出统计数据 as dump
}

User -> setArgs
User -> selectFile
User -> scanFile

App -> statistics
App -> dump

@enduml

@startuml 活动图_流程图
start
:准备测试文件;

:点击app启动;

repeat :开始测试;

        partition TODO {
                :点击选择待扫描文件/目录;
        }

        :开始扫描;

        fork
                :回调进度显示;

        fork again
        partition TODO {
                fork
                        :记录文件扫描完成时间;
                fork again
                        :记录CPU/内存使用情况;
                end fork

                :合并两个时间为统计数据文件;
        }
        end fork

        :扫描结束;

repeat while (继续扫描) is (是) not(否)

end
@enduml