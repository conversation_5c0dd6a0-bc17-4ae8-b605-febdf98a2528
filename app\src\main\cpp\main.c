//
// Created by zos on 2024/8/8.
//

//#include "argparse.h"
#include <android/log.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <unistd.h>

#define LOG_TAG "com.antiy.cmd"

#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

#define PID_STAT_PATH "/proc/%d/stat"
#define SYS_STAT_PATH "/proc/stat"

static int pid = 0;

typedef struct {
        unsigned long long utime, stime;
        struct timeval     tv;
} PID_STAT;

typedef struct {
        unsigned long long user, nice, system, idle, iowait, irq, softirq;
        struct timeval     tv;
} SYS_STAT;

unsigned long long pid_time(PID_STAT pid_stat) { return pid_stat.utime + pid_stat.stime; }

unsigned long long sys_time(SYS_STAT sys_stat)
{
        return sys_stat.user + sys_stat.nice + sys_stat.system + sys_stat.idle + sys_stat.iowait + sys_stat.irq + sys_stat.softirq;
}

// check file exist or not
bool file_exists(char* path) { return access(path, F_OK) == 0; }

// parse pid stat info into struct pid_stat
bool parse_pid_stat(FILE* fd, PID_STAT* pid_stat)
{
        if (fd == NULL || pid_stat == NULL) return false;

        gettimeofday(&pid_stat->tv, NULL);

        /*
              /proc/<pid>/stat
              利用 scanf 直接解析字符串内容

              pid(d) (comm(s)) state(c) ppid(d) pgrp(d)
              session(d) tty_nr(d) tpgrd(d) flags(u) minflt(lu)

              cminflt(lu) majflt(lu) cmajflt(lu) utime(lu) stime(lu)  ----> here utime & stime is what we needed
              cutime(ld) cstime(ld) priority(ld) nice(ld) num_threads(ld)

              itrealvalue(ld) starttime(llu) vsize(lu) rss(ld) rsslim(lu)
              startcode(lu) endcode(lu) startstack(lu) kstkesp(lu) kstkeip(lu)

              signal(lu) blocked(lu) sigignore(lu) sigcatch(lu) wchan(lu)
              nswap(lu) cnswap(lu) exit_signal(d) processor(d) rt_priority(u)

              policy(u) delayacct_blkio_ticks(llu) guest_time(lu) cguest_time(ld) start_data(lu)
              end_data(lu) start_brk(lu) arg_start(lu) arg_end(lu) env_start(llu)

              env_end(lu) exit_code(d)
        */
        int nitems = fscanf(fd,
                            "%*d %*s %*c %*d %*d"
                            " %*d %*d %*d %*u %*lu"
                            " %*lu %*lu %*lu %llu %llu"
                            " %*ld %*ld %*ld %*ld %*ld"
                            " %*ld %*llu %*s",
                            &pid_stat->utime,
                            &pid_stat->stime);

        return nitems == 2;
};

/**
 * @brief calc the pid delta
 *
 * @param before
 * @param now
 * @return * int; value of delta, -1 if `before` or `now` is not set or input args are null.
 */
int get_pid_delta(PID_STAT* now, PID_STAT* before)
{
        if (before == NULL || now == NULL) return -1;
        if (before->tv.tv_sec == 0 || now->tv.tv_sec == 0) return -1;

        unsigned long long delta = (now->utime + now->stime) - (before->utime + before->stime);
        return (int)delta;
}

/**
 * @brief parse system stat file to get cpu time.
 *
 * @param sys_stat pointer to struct SYS_STAT into which the cpu stat info stores.
 * @return true
 * @return false
 */
bool parse_sys_stat(SYS_STAT* sys_stat)
{
        FILE* fd = fopen(SYS_STAT_PATH, "r");
        if (fd == NULL || sys_stat == NULL) {
                LOGE("Failed to open %s or input struct sys_stat is null.", SYS_STAT_PATH);
                return false;
        }

        gettimeofday(&sys_stat->tv, NULL);

        int nitems = fscanf(fd,
                            "%*s %llu %llu %llu %llu %llu %llu %llu %*s",
                            &sys_stat->user,
                            &sys_stat->nice,
                            &sys_stat->system,
                            &sys_stat->idle,
                            &sys_stat->iowait,
                            &sys_stat->irq,
                            &sys_stat->softirq);
        fclose(fd);
        return nitems == 7;
}

/**
 * @brief get the delta time of cpu
 *
 * @param before
 * @param now
 * @return int ; value of delta, -1 for invalid input or empty `before` or `now`
 */
int get_cpu_delta(SYS_STAT* now, SYS_STAT* before)
{
        if (before == NULL || now == NULL) return -1;
        if (before->tv.tv_sec == 0 || now->tv.tv_sec == 0) return -1;

        unsigned long long delta = (now->idle - before->idle) + (now->iowait - before->iowait) + (now->irq - before->irq) + (now->nice - before->nice) +
                                   (now->softirq - before->softirq) + (now->system - before->system) + (now->user - before->user);

        return (int)delta;
}

// 命令行帮助
static const char* const usages[] = {
    "cpu_usage [options] [[--] args]",
    "cpu_usage [options]",
    NULL,
};

// signal handler
void sig_handler(int signo)
{
        if (signo == SIGUSR1) {
                kill(pid, SIGCONT);
                LOGD("got signal sigquit!");

                exit(0);
        }
}

int main(int argc, char* argv[])
{
        /*
         * 注意，本程序接受如下参数:
         *   -p <pid> 要监控的pid，必选参数，int。
         *   -c <percent> 目标CPU资源占用率，百分比。可选，默认为5（即5%），flaot。
         *   -i <interval> 监控轮训间隔，单位微秒，1000微秒=1毫秒。可选，默认为500微秒，int。
         *
         *   TODO: 当前暂不考虑多线程的情况，认为目标进程为单线程。
         */

        /** 注册信号处理函数 **/
        if (signal(SIGUSR1, sig_handler) == SIG_ERR)
                LOGD("can't catch SIGQUIT");
        else
                LOGD("signal siguser registered!");

        /**  解析命令行参数  **/

        // 初始化一些变量
        float                  percent   = 9;
        int                    interval  = 50;
        struct argparse_option options[] = {
            OPT_HELP(),
            OPT_GROUP("Basic options"),
            OPT_INTEGER('p', "pid", &pid, "pid to monitor, required.", NULL, 0, 0),
            OPT_FLOAT('P', "percent", &percent, "percent throttle, optional, default is 5, as 5%, range 1-100.", NULL, 0, 0),
            OPT_INTEGER('i', "interval", &interval, "interval of monitoring, optional, default is 50, as 50ms, range 10-1000", NULL, 0, 0),
            OPT_END(),
        };
        struct argparse argparser;

        // 执行解析操作
        argparse_init(&argparser, options, usages, 0);
        argparse_describe(&argparser, "\nthis is a cpu monitor program to hold target pid using cpu under specific percentage.", NULL);
        argc = argparse_parse(&argparser, argc, (const char**)argv);
        if (argc != 0) {
                // there's extra argument
                LOGD("There are %d extra args", argc);
                for (int i = 0; i < argc; i++) { LOGD("\targ %d: %s", i + 1, *(argv + i)); }
        }
        LOGD("Got args: pid[%d], percent[%f], interval[%d]", pid, percent, interval);
        // 修复超出范围的参数
        if (percent < 1) percent = 1;
        if (percent > 100) percent = 100;
        if (interval < 10) interval = 10;
        if (interval > 1000) interval = 1000;

        /**  准备工作  **/
        // /proc/<pid>/stat file name
        char pid_stat_path[255] = {0};
        sprintf(pid_stat_path, PID_STAT_PATH, pid);
        // fds to operate
        FILE* pid_fd = NULL;

        // pid stat & sys stat info var init
        PID_STAT pid_stat_before, pid_stat_now;
        SYS_STAT sys_stat_before, sys_stat_now;
        memset(&pid_stat_now, 0, sizeof(PID_STAT));
        memset(&sys_stat_now, 0, sizeof(SYS_STAT));

        // toggle stat
        bool is_stopped = false;

        // 执行主循环
        for (;;) {
                /**
                 * 逻辑：
                 *      1. 记录当前进程时间信息/CPU时间信息
                 *      2. 计算delta（如果没有上一次，直接跳到sleep阶段）
                 *      3. 判断delta值，超过阈值，发送sigstop，并进入sleep
                 *      4. 没超过阈值，发送sigcont，保存当前为历史值
                 *      5. sleep
                 */

                /*  1.记录当前信息  */
                if (!file_exists(pid_stat_path)) {
                        // 如果pid状态文件不存在，说明进程不存在，直接退出
                        LOGE("File %s not exist.", pid_stat_path);
                        break;
                }
                pid_fd = fopen(pid_stat_path, "r");
                if (pid_fd == NULL) {
                        // 同上，如果pid状态文件无法打开，也认为不存在，退出
                        LOGE("Cannot open file %s", pid_stat_path);
                        break;
                }

                /* 2. 计算delta */
                if (!parse_pid_stat(pid_fd, &pid_stat_now)) {
                        LOGE("Failed to parse pid stat!");
                        fclose(pid_fd);
                        break;
                }
                fclose(pid_fd);

                if (!parse_sys_stat(&sys_stat_now)) {
                        LOGE("Failed to parse system stat!");
                        break;
                }
                int delta_pid = get_pid_delta(&pid_stat_now, &pid_stat_before);
                int delta_cpu = get_cpu_delta(&sys_stat_now, &sys_stat_before);

                /* 3. 判断delta */
                if (delta_cpu > 0 && delta_pid > 0 && delta_pid * 100 / delta_cpu > percent) {
                        // LOGE("delta: %d, cpu: %d", delta_pid, delta_cpu);
                        // delta大于阈值
                        kill(pid, SIGSTOP);
                }
                else {
                        // LOGD("delta: %d, cpu: %d", delta_pid, delta_cpu);
                        // delta小于阈值
                        kill(pid, SIGCONT);
                        memcpy(&pid_stat_before, &pid_stat_now, sizeof(PID_STAT));
                        memcpy(&sys_stat_before, &sys_stat_now, sizeof(SYS_STAT));
                }

                /* 4. sleep */
                usleep(interval * 1000);
        }

        return 0;
}