#include <stdio.h>
#include <dlfcn.h>
#include <stdlib.h>
#include "jni.h"
#include <string.h>
#include "vlog.h"

struct _DetApkInfoResult {
    char *pcPackageName;
    char *pcMfMd5;
    char *pcNewKeyHash;
    char *pcKeySetHash;
};
typedef struct _DetApkInfoResult DetApkInfo;


typedef DetApkInfo *(*AVLM_GetApkInfo_FUNC)(char *);

typedef void (*AVLM_FreeApkInfo_FUNC)(DetApkInfo *);

AVLM_GetApkInfo_FUNC fAVLM_GetApkInfo = NULL;
AVLM_FreeApkInfo_FUNC fAVLM_FreeApkInfo = NULL;

static jstring getApkInfo(JNIEnv *env, jclass type, jstring apkPath) {
    void *pvHandler = dlopen("libavlmc.so", RTLD_NOW);
    if (!pvHandler) {
        LOGD("ERR:%s\n", dlerror());
        return 0;
    }
    fAVLM_GetApkInfo = (AVLM_GetApkInfo_FUNC) dlsym(pvHandler, "AVLM_GetApkInfo");
    fAVLM_FreeApkInfo = (AVLM_FreeApkInfo_FUNC) dlsym(pvHandler, "AVLM_FreeApkInfo");
    if (!fAVLM_GetApkInfo || !fAVLM_FreeApkInfo) {
        LOGD("fun load error %s\n", "");
        dlclose(pvHandler);
        return 0;
    }
    char *jApkPath = NULL;
    jApkPath = (char *) (*env)->GetStringUTFChars(env, apkPath, 0);
    DetApkInfo *pInfo = fAVLM_GetApkInfo(jApkPath);
    jstring retJstring = (*env)->NewStringUTF(env, "");
    if (pInfo) {
        int apkInfoSize = 0;
        if (pInfo->pcMfMd5 != NULL) {
            apkInfoSize += strlen(pInfo->pcMfMd5);
        }
        if (pInfo->pcNewKeyHash != NULL) {
            apkInfoSize += strlen(pInfo->pcNewKeyHash);
        }
        if (pInfo->pcKeySetHash != NULL) {
            apkInfoSize += strlen(pInfo->pcKeySetHash);
        }
        apkInfoSize += strlen(";;");
        char *apkInfo = malloc(apkInfoSize + 1);
        sprintf(apkInfo, "%s;%s;%s", pInfo->pcKeySetHash, pInfo->pcMfMd5,
                pInfo->pcNewKeyHash);
        apkInfo[apkInfoSize] = '\0';
        retJstring = (*env)->NewStringUTF(env, apkInfo);
        free(apkInfo);
        apkInfo = NULL;
        fAVLM_FreeApkInfo(pInfo);
    }

    dlclose(pvHandler);
    return retJstring;
}

static int __attribute__ ((visibility("hidden")))
jniRegisterNativeMethods(JNIEnv *env, jclass cls, const JNINativeMethod *gMethods, int numMethods) {
    if (cls == NULL) {
        return -1;
    }

    int result = 0;
    if ((*env)->RegisterNatives(env, cls, gMethods, numMethods) < 0) {
        result = -1;
    }

    (*env)->DeleteLocalRef(env, cls);
    return result;
}

static JNINativeMethod jni_Methods_table[] = {
        {"getApkInfo", "(Ljava/lang/String;)Ljava/lang/String;", (void *) getApkInfo}
};

JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *reserved) {
    JNIEnv *env;
    if ((*vm)->GetEnv(vm, (void **) &env, JNI_VERSION_1_4) != JNI_OK) {
        return -1;
    }

    jclass cls = (*env)->FindClass(env, "com/antiy/avlsdk_cloud/AVLEngineCloud");
    jniRegisterNativeMethods(env, cls, jni_Methods_table,
                             sizeof(jni_Methods_table) / sizeof(JNINativeMethod));

    return JNI_VERSION_1_4;
}


