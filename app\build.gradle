apply plugin: 'com.android.application'

//通过指令获取当前分支提交次数commitCount
Process process = "git rev-list --count HEAD".execute()
process.waitFor()
int commitCount = process.getText() as int

logger.warn("BUILDING VERSION {}", commitCount)

android {
    signingConfigs {
        release {
            keyAlias 'apt'
            keyPassword 'ccs2021'
            storePassword 'ccs2021'
            storeFile file('../ccsapt.keystore')
        }
    }
    namespace "com.gkuw.szytq"
    compileSdkVersion 31
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    defaultConfig {
        applicationId "com.gkuw.szytq"
        minSdkVersion 21
        targetSdkVersion 31
        versionCode commitCount
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        sourceSets {
            main {
                jniLibs.srcDir(['libs'])
            }
        }

        ndk {
//            abiFilters "armeabi"
            abiFilters "arm64-v8a"
        }
    }
    ndkVersion "27.0.12077973"

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources false
            zipAlignEnabled true
            signingConfig signingConfigs.release

            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            applicationVariants.all {
                variant ->
                    variant.outputs.all {
                        outputFileName = "${project.archivesBaseName}-v${defaultConfig.versionName}.${defaultConfig.versionCode}.apk"
                    }
            }
        }
    }
//    externalNativeBuild {
//        cmake {
//            path file('CMakeLists.txt')
//        }
//    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation project(path: ':avlsdk')
    implementation project(path: ':avlsdk_pc')
    implementation project(path: ':avlsdk_cloud')

    implementation 'androidx.core:core-ktx:1.1.0'
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'



    implementation 'io.github.mayampi01:com.tbruyelle.rxpermissions2:0.9.5'
    implementation 'io.reactivex.rxjava2:rxjava:2.1.8'
    implementation 'io.reactivex.rxjava2:rxandroid:2.0.1'

}
