package com.gkuw.szytq.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

class FileFormatChecker {
    public static boolean isMp4File(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("File does not exist: " + filePath);
        }
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] header = new byte[8];
            fis.read(header);
            String headerHex = bytesToHex(header);
            return headerHex.equals("0000001866747970");
        } catch (IOException e) {
            throw new RuntimeException("Failed to read file: " + filePath, e);
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }
}
