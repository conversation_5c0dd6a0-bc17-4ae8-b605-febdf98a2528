package com.gkuw.szytq;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.antiy.avlsdk_pc.AssetsUtil;

import java.io.File;

public class CpuUsageMonitor {
    private static final String TAG = CpuUsageMonitor.class.getSimpleName();

    public static String mCpuUsagePath;

    private static CpuUsageMonitor mInstance;

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static void init(Context context) {
        // prepare the binary that we will use.
        File cpu_usage = new File(context.getFilesDir().getAbsoluteFile() + "/cpu_usage");
        mCpuUsagePath = cpu_usage.getAbsolutePath();

        // NOTE: if we check the file existence, app update will cause in-compatible `cpu_usage` file
        //     remains on disk, so here we forcibly copy the binary everytime we got inited.
        AssetsUtil.copyAssetFileToFilesDir(context, String.format("%s/main",
                Build.SUPPORTED_ABIS[0]),
                context.getFilesDir() + "/cpu_usage");

        if (!cpu_usage.canExecute()) {
            cpu_usage.setExecutable(true);
        }

        Log.d(TAG, String.format("File %s, executable: %b", mCpuUsagePath, cpu_usage.canExecute()));

        return;
    }

    /**
     * return a single instance of class CpuUsageMonitor.
     * @return object of class CpuUsageMonitor
     */
    public static CpuUsageMonitor getInstance() {
        // single instance
        if (mInstance == null) {
            synchronized (AvlVirusEngine.class) {
                if (mInstance == null) {
                    mInstance = new CpuUsageMonitor();
                }
            }
        }

        return mInstance;
    }
}
