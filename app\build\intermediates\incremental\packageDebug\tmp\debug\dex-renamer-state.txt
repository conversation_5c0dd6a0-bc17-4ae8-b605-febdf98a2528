#Mon Jun 16 17:44:10 CST 2025
base.0=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.2=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\11\\classes.dex
base.3=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\12\\classes.dex
base.4=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\15\\classes.dex
base.5=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\3\\classes.dex
base.6=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\8\\classes.dex
base.7=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.8=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
base.9=D\:\\Projects\\Android\\riskdemo\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=11/classes.dex
path.3=12/classes.dex
path.4=15/classes.dex
path.5=3/classes.dex
path.6=8/classes.dex
path.7=0/classes.dex
path.8=5/classes.dex
path.9=7/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
