[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: arm64-v8a", "file_": "D:\\Projects\\Android\\riskdemo\\avlsdk_pc\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 490010504}, {"level_": 0, "message_": "JSON 'D:\\Projects\\Android\\riskdemo\\avlsdk_pc\\build\\.cxx\\Debug\\122v3xh2\\arm64-v8a\\android_gradle_build.json' was up-to-date", "file_": "D:\\Projects\\Android\\riskdemo\\avlsdk_pc\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1165279055}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Projects\\Android\\riskdemo\\avlsdk_pc\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1592716583}]