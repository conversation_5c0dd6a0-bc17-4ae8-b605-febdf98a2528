package com.gkuw.szytq;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;

import com.tbruyelle.rxpermissions2.RxPermissions;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {

    public static final String PREF_SLEEP = "sleep_scheme";
    public static final String PREF_USEC = "sleepMacroSec";

    public static final String PREF_CPULIMIT = "cpulimit_scheme";
    public static final String PREF_PERCENT = "percent";
    public static final String PREF_INTERVAL = "interval";

    public static final int PREF_MIN_SEC = 0;
    public static final int PREF_MAX_SEC = Integer.MAX_VALUE / 1000;

    private static String TAG = "AVLVirus Scan Test";
    private static Leak sLeak;
    int totalCount = 0;
    long patchScanSingleStart = 0;
    long patchScanSingleEnd = 0;
    long allScanStart = 0;
    long allScanEnd = 0;
    FileOutputStream outputStream = null;
    FileOutputStream profileStream = null;
    private TextView mTv;

    private Button mBtnScanFile;
    private Button mBtnScanDir;

    private Button mBtnSetJar, mBtnSetDex, mBtnSetApk;
    private Button mBtnSetMp3, mBtnSetMp4, mBtnSetJpg, mBtnSetPng, mBtnSetGif, mBtnSetElf;
    private Button mBtnMultimedia;

    private EditText mEdt1;
    private Handler mHandler;
    private Handler mPopupHandler;
    private Handler leakHandler = new Handler();

    private Switch mSwitchToggleSleep;
    private EditText mInputSleepMacroSec;
    private TextView mInputSleepMacroSecLabel;
    private Button mBtnSleepMacroSecConfirm, mBtnSleepMacroSecReset;
    private SharedPreferences sharedPreferences;

    private Switch mSwitchToggleCpulimit;
    private EditText mInputPercent, mInputInterval;
    private Button mBtnPercentConfirm, mBtnIntervalConfirm;
    private Button mBtnMonitorReset;

    @SuppressLint("CheckResult")
    private void initPermission() {
        RxPermissions rxPermissions = new RxPermissions(this);
        rxPermissions.requestEach(Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_PHONE_STATE)
                .subscribe(permission -> {
                    if (permission.granted) {
                        return;
                    }
                    if (permission.shouldShowRequestPermissionRationale) {
                        return;
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e("ALEAK", "onDestroy");
    }

    private void scanAll(String dir) {

        new Thread(new Runnable() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void run() {

//                List<String> sizes = new ArrayList<>();
//                sizes.add("1M");
//                sizes.add("10M");
//                sizes.add("100M");
//                sizes.add("1G");

                String targetDir = dir;
                Log.d(TAG, "targetDir" + dir);
                List<String> apks = new ArrayList<>();
                apks.add(targetDir);

                if (!new File(targetDir).exists()) {
                    Message msg = new Message();
                    msg.obj = "目录不存在: " + targetDir;
                    mHandler.sendMessage(msg);
                    return;
                }

                Message msg = new Message();
                msg.obj = "开始批量扫描, 目录为：" + targetDir;
                mHandler.sendMessage(msg);

                AvlVirusEngine.getInstance().scanAll(targetDir, new AvlVirusScanListener() {
                    @Override
                    public void scanStart() {
                        Log.d(TAG, "scanStart");
                        allScanStart = System.currentTimeMillis();
                    }

                    @Override
                    public void scanCount(int i) {
                        totalCount = i;
                        Log.d(TAG, "total " + i);
                    }

                    @Override
                    public void scanSingleIng(String name, String name2, String samplePath) {
                        patchScanSingleStart = System.currentTimeMillis();
                    }

                    @Override
                    public void scanSingleEnd(AvlVirusScanResult scanResult) {
                        patchScanSingleEnd = System.currentTimeMillis();
                        File file = new File(scanResult.getPath());
                        String result = "未知样本";
                        if (scanResult.getVirusName() != null) {
                            Log.d(TAG,
                                    scanResult.getPath() + ", 检出 " + scanResult.getVirusName());
                            result = scanResult.getVirusName();
                        }

                        String prompt =
                                "扫描： " + scanResult.getPath() + " 大小： " + file.length() + " " +
                                        "耗时：" + (patchScanSingleEnd - patchScanSingleStart) + "(ms), 检测结果为：" + result;
                        Message msg = new Message();
                        msg.obj = prompt;
                        mHandler.sendMessage(msg);

                        try {
                            outputStream.write(prompt.getBytes());
                            outputStream.write("\n".getBytes());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void scanStop() {
                        allScanEnd = System.currentTimeMillis();
                    }

                    @Override
                    public void scanFinished() {
                        allScanEnd = System.currentTimeMillis();
                        Message msg = new Message();
                        msg.obj =
                                "扫描完成，总个数：" + totalCount + " ，总时长： " + (allScanEnd - allScanStart) + "(ms)";
                        mHandler.sendMessage(msg);
                        Log.d(TAG, "扫描完成");
                    }

                    @Override
                    public void onCrash() {
                        allScanEnd = System.currentTimeMillis();
                    }
                });

                try {
                    Thread.sleep(3000);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                mPopupHandler.sendMessage(new Message());
            }
        }).start();
    }

    private String checkAndGetInputFile() {
        return checkAndGetInput(true);
    }

    private String checkAndGetInputDir() {
        return checkAndGetInput(false);
    }

    private String checkAndGetInput(boolean is_file) {
        String path = mEdt1.getText().toString();

        if (path.isEmpty())
            path = is_file ? "/sdcard/samples/sample.apk" : "/sdcard/kimi/";

        File d = new File(path);
        if (!d.exists()) {
            return null;
        }

        return d.getAbsolutePath();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        sharedPreferences = getSharedPreferences("preference", MODE_PRIVATE);

        initPermission();

        mTv = findViewById(R.id.tv_prompt);
        /**
         * 组件初始化
         */
        // 扫描类型
        mBtnScanDir = findViewById(R.id.btn_scanDir);

        mBtnSetJar = findViewById(R.id.btn_setJar);
        mBtnSetDex = findViewById(R.id.btn_setDex);
        mBtnSetApk = findViewById(R.id.btn_setApk);

        mBtnSetMp3 = findViewById(R.id.btn_setMp3);
        mBtnSetMp4 = findViewById(R.id.btn_setMp4);
        mBtnSetJpg = findViewById(R.id.btn_setJpg);
        mBtnSetPng = findViewById(R.id.btn_setPng);
        mBtnSetGif = findViewById(R.id.btn_setGif);
        mBtnSetElf = findViewById(R.id.btn_setElf);
        mBtnMultimedia = findViewById(R.id.btn_setMultimedia);

        // 扫描路径
        mEdt1 = findViewById(R.id.edt_scanDir);

        // sleep模式设置
        mSwitchToggleSleep = findViewById(R.id.toggleSleep);
        mInputSleepMacroSec = findViewById(R.id.inputSleepMacroSec);
        mInputSleepMacroSecLabel = findViewById(R.id.labelSleepMacroSec);
        mBtnSleepMacroSecConfirm = findViewById(R.id.btnSleepMicroSecConfirm);
        mBtnSleepMacroSecReset = findViewById(R.id.btnSleepMacroSecReset);

        // monitor模式设置
        mSwitchToggleCpulimit = findViewById(R.id.toggleCpuLimit);
        mInputInterval = findViewById(R.id.inputInterval);
        mInputPercent = findViewById(R.id.inputPercent);
        mBtnIntervalConfirm = findViewById(R.id.mBtnConfirmInterval);
        mBtnPercentConfirm = findViewById(R.id.mBtnConfirmPercent);
        mBtnMonitorReset = findViewById(R.id.mBtnResetMonitor);

       /* mHandler = new Handler() {
            @Override
            public void handleMessage(@NonNull Message msg) {
                String msgText = (String) msg.obj;
                if (!msgText.endsWith("\n"))
                    msgText += "\n";

                mTv.append(msgText);

                super.handleMessage(msg);
            }
        };*/

        mHandler = new Handler(new Handler.Callback() {
            @Override
            public boolean handleMessage(@NonNull Message message) {
                mTv.setText((String) message.obj);
                return false;
            }
        });
        mPopupHandler = new Handler() {
            @Override
            public void handleMessage(@NonNull Message msg) {
                super.handleMessage(msg);
                AlertDialog dialog = new AlertDialog.Builder(MainActivity.this)
                        .setTitle("扫描完成").setMessage("扫描完成")
                        .setCancelable(true)
                        .create();
                dialog.show();

                Toast.makeText(MainActivity.this,"扫描完成", Toast.LENGTH_LONG);
            }
        };
        try {
            String path = "avl_virus_out.txt";
            File file = new File(this.getExternalCacheDir() + File.separator + path);
            if (!file.exists()) {
                file.createNewFile();
            }
            Log.d(TAG, file.getAbsolutePath());
            outputStream = new FileOutputStream(file);
        } catch (IOException e) {
            Log.d(TAG, e.getMessage());
            e.printStackTrace();
        }

        try {
            String path = "avl_profile.txt";
            File file = new File("/sdcard/" + path);
            if (!file.exists()) {
                file.createNewFile();
            }

            Log.d(TAG, String.format("Profile result: %s", file.getAbsolutePath()));
            profileStream = new FileOutputStream(file);
        } catch (IOException e) {
            Log.d(TAG, e.getMessage());
            e.printStackTrace();
        }

        /**
         * 组件功能设置
         */
        // 开始扫描
        mBtnScanDir.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String dir = checkAndGetInputDir();

                if (dir == null) {
                    Message msg = new Message();
                    msg.obj = "目录不存在 " + mEdt1.getText().toString();
                    mHandler.sendMessage(msg);
                    return;
                }

                scanAll(dir);
            }
        });

        // 设置不同的扫描目录
        mBtnSetJar.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/jar");
        });
        mBtnSetDex.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/dex");
        });
        mBtnSetApk.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/apk");
        });
        mBtnSetMp3.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/mp3");
        });
        mBtnSetMp4.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/mp4");
        });
        mBtnSetJpg.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/jpg");
        });
        mBtnSetPng.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/png");
        });
        mBtnSetGif.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/gif");
        });
        mBtnSetElf.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/elf");
        });
        mBtnMultimedia.setOnClickListener(view -> {
            mEdt1.setText("/sdcard/samples/mm");
        });

        // 设置休眠扫描的间隔
        mSwitchToggleSleep.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                SharedPreferences.Editor editor = sharedPreferences.edit();
                editor.putBoolean(PREF_SLEEP, b);
                editor.commit();

                AvlVirusEngine.getInstance().updateConfig(sharedPreferences);
            }
        });
        mSwitchToggleSleep.setChecked(sharedPreferences.getBoolean(PREF_SLEEP, false));
        mInputSleepMacroSec.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    int val = Integer.parseInt(s.toString());
                    if (val > PREF_MAX_SEC) {
                        String max = String.format("%d", PREF_MAX_SEC);
                        s.replace(0, s.length(), max, 0, max.length());
                    } else if (val < 0) {
                        s.replace(0, s.length(), "0", 0, 1);
                    }
                } catch (NumberFormatException ex) {
                    // Do something
                }
            }
        });
        mInputSleepMacroSec.setText(String.format("%d", sharedPreferences.getInt(PREF_USEC, 10)));
        mBtnSleepMacroSecReset.setOnClickListener(view -> {
            mInputSleepMacroSec.setText("10");
        });
        mBtnSleepMacroSecConfirm.setOnClickListener(view -> {
            int sleepUSec = Integer.parseInt(mInputSleepMacroSec.getText().toString());

            if (sleepUSec < 0) {
                sleepUSec = 0;
                mInputSleepMacroSec.setText("0");
            } else if (sleepUSec > PREF_MAX_SEC) {
                sleepUSec = PREF_MAX_SEC;
                mInputSleepMacroSec.setText(String.format("%d", PREF_MAX_SEC));
            }

            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putInt(PREF_USEC, sleepUSec);
            editor.commit();

            AvlVirusEngine.getInstance().updateConfig(sharedPreferences);
        });

        // 设置cpu监视的百分比
        mSwitchToggleCpulimit.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                SharedPreferences.Editor editor = sharedPreferences.edit();
                editor.putBoolean(PREF_CPULIMIT, b);
                editor.commit();

                AvlVirusEngine.getInstance().updateConfig(sharedPreferences);
            }
        });
        mInputPercent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    int val = Integer.parseInt(s.toString());
                    if (val > 100) {
                        String max = String.format("%d", 100);
                        s.replace(0, s.length(), max, 0, max.length());
                    } else if (val < 0) {
                        s.replace(0, s.length(), "0", 0, 1);
                    }
                } catch (NumberFormatException ex) {
                    // Do something
                }
            }
        });
        mInputPercent.setText(String.format("%d", sharedPreferences.getInt(PREF_PERCENT, 9)));
        mBtnPercentConfirm.setOnClickListener(view -> {
            int percent = Integer.parseInt(mInputPercent.getText().toString());

            if (percent < 0) {
                percent = 0;
                mInputPercent.setText("0");
            } else if (percent > 100) {
                percent = 100;
                mInputPercent.setText("100");
            }

            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putInt(PREF_PERCENT, percent);
            editor.commit();

            AvlVirusEngine.getInstance().updateConfig(sharedPreferences);
        });

        // 设置cpu监视进程的interval
        mInputInterval.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    int val = Integer.parseInt(s.toString());
                    if (val > 1000) {
                        String max = String.format("%d", 1000);
                        s.replace(0, s.length(), max, 0, max.length());
                    } else if (val < 10) {
                        s.replace(0, s.length(), "10", 0, 2);
                    }
                } catch (NumberFormatException ex) {
                    // Do something
                }
            }
        });
        mInputInterval.setText(String.format("%d", sharedPreferences.getInt(PREF_PERCENT, 50)));
        mBtnIntervalConfirm.setOnClickListener(view -> {
            int interval = Integer.parseInt(mInputInterval.getText().toString());

            if (interval < 10) {
                interval = 10;
                mInputInterval.setText("10");
            } else if (interval > 1000) {
                interval = 1000;
                mInputInterval.setText("1000");
            }

            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putInt(PREF_INTERVAL, interval);
            editor.commit();

            AvlVirusEngine.getInstance().updateConfig(sharedPreferences);
        });

        AvlVirusEngine.getInstance().updateConfig(sharedPreferences);
    }

    private class Leak {
        private void test() {
            Log.i("TEST", "test method execute");
        }
    }
}