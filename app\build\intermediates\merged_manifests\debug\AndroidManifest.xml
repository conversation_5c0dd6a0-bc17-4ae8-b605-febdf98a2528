<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.gkuw.szytq"
    android:versionCode="21"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="31" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        android:minSdkVersion="30" />

    <permission
        android:name="com.gkuw.szytq.permission.ANTI_VIRUS"
        android:protectionLevel="signature" />

    <application
        android:name="com.gkuw.szytq.MyApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/AppTheme" >
        <activity
            android:name="com.gkuw.szytq.MainActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="AVL_SDK_APPKEY"
            android:value="C33742EFF27CCD51BA3EC1FB0E6FE22E" />
        <meta-data
            android:name="com.antiy.ccs.app.App"
            android:value="Application" />

        <provider
            android:name="com.avl.engine.security.AVLProvider"
            android:authorities="com.gkuw.szytq.AVLprovider"
            android:permission="com.gkuw.szytq.permission.ANTI_VIRUS" />
    </application>

</manifest>