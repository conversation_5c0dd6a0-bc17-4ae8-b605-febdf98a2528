LOCAL_PATH := $(call my-dir)
MAIN_LOCAL_PATH := $(call my-dir)
include $(CLEAR_VARS)
LOCAL_MODULE := avlsdk_pc

LOCAL_CFLAGS := -Wno-error=format-security -fpermissive -DLOG_TAG=\"AVLSDK_PC\"
LOCAL_CFLAGS += -fno-rtti -fno-exceptions

LOCAL_C_INCLUDES += $(MAIN_LOCAL_PATH)
LOCAL_C_INCLUDES += $(MAIN_LOCAL_PATH)/interface

LOCAL_SRC_FILES := demo.c \

LOCAL_LDLIBS := -llog -latomic

include $(BUILD_SHARED_LIBRARY)
