package com.antiy.avlsdk_pc;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class AssetsUtil {
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void copyAssetFileToFilesDir(Context context, String assetFileName, String destinationFileName) {
        AssetManager assetManager = context.getAssets();
        File filesDir = context.getFilesDir();
        File file = new File(destinationFileName);

        try (InputStream is = assetManager.open(assetFileName);
             OutputStream os = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int read;
            while ((read = is.read(buffer)) != -1) {
                os.write(buffer, 0, read);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void copyFilesFromAssets(Context context, String assetsDirectoryPath, String destinationDirectoryPath) {
        AssetManager assetManager = context.getAssets();
        try {
            String[] files = assetManager.list(assetsDirectoryPath);
            if (files.length > 0) {
                File file = new File(destinationDirectoryPath);
                file.mkdirs();
                for (String fileName : files) {
                    copyFilesFromAssets(context, assetsDirectoryPath + "/" + fileName, destinationDirectoryPath + "/" + fileName);
                }
            } else {
                InputStream is = context.getAssets().open(assetsDirectoryPath);
                FileOutputStream fos = new FileOutputStream(new File(destinationDirectoryPath));
                byte[] buffer = new byte[1024];
                int byteCount = 0;

                while ((byteCount = is.read(buffer)) != -1) {
                    fos.write(buffer ,0, byteCount);
                }

                fos.flush();
                is.close();
                fos.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
