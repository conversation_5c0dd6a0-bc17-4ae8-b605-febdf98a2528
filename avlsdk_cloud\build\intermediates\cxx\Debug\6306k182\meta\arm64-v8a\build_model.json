{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\.cxx\\Debug\\6306k182\\arm64-v8a", "soFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates\\cxx\\Debug\\6306k182\\obj\\local\\arm64-v8a", "soRepublishFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates\\ndkBuild\\debug\\obj\\local\\arm64-v8a", "abiPlatformVersion": 21, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "soFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates\\cxx\\Debug\\6306k182\\obj\\local", "soRepublishFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates\\ndkBuild\\debug\\obj\\local", "cxxBuildFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\.cxx\\Debug\\6306k182", "intermediatesFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates\\cxx\\Debug\\6306k182", "isDebuggableEnabled": true, "validAbiList": ["ARM64_V8A"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\.cxx", "intermediatesBaseFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates", "intermediatesFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates\\cxx", "gradleModulePathName": ":avlsdk_cloud", "moduleRootFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud", "moduleBuildFile": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build.gradle", "makeFile": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\src\\main\\jni\\Android.mk", "buildSystem": "NDK_BUILD", "ndkFolder": "D:\\Android\\Sdk\\ndk\\27.0.12077973", "ndkVersion": "27.0.12077973", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "SYSTEM", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\Projects\\Android\\riskdemo", "sdkFolder": "D:\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "ideBuildTargetAbi": "arm64-v8a,armeabi-v7a,armeabi", "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "nativeBuildOutputLevel": "QUIET"}, "prefabClassPathFileCollection": [], "prefabPackageDirectoryListFileCollection": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\.cxx\\Debug\\6306k182\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "6306k182s59m1d2st1p2441vm3r1r204x3n3d284q1p95ns4yu2452", "configurationArguments": ["NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\src\\main\\jni\\Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-21", "NDK_OUT=D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates\\cxx\\Debug\\6306k182/obj", "NDK_LIBS_OUT=D:\\Projects\\Android\\riskdemo\\avlsdk_cloud\\build\\intermediates\\cxx\\Debug\\6306k182/lib"]}